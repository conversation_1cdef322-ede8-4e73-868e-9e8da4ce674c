# Product Requirements Document (PRD)

**Product:** Techrar Gym Customer Mobile Application

**Platform:** iOS & Android (React Native recommended)  
**Audience:** Gym Customers (Members)  
**Delivery:** Mobile App (integrates with Techrar Gym Management API)

---

## Overview

The Techrar Gym Customer Mobile App enables gym members to manage their fitness journey: register, log in, view and update their profile, browse and book classes, manage subscriptions, and receive notifications. The app is designed for efficiency, clarity, and a delightful user experience, supporting both Arabic and English.

---

## Core Features

### 1. Authentication & Onboarding

- Merchant selection (enter/select gym/merchant)
- Customer registration and login (email, password, merchant-id)
- Password reset
- Secure JWT token/session management

### 2. Profile Management

- View and update profile (name, phone, gender, birthday, profile image)
- Upload/change profile image
- Localization: Arabic and English (RTL/LTR)

### 3. Class Discovery & Booking

- Browse available classes (filter by branch, date, trainer, difficulty, type)
- View class details and schedule
- Book classes
- View/manage upcoming and past bookings
- Real-time feedback on booking status (confirmed, waitlisted, full, etc.)

### 4. Subscription Management

- View available subscription plans
- View plan details
- Purchase subscription plans (select plan, payment method, promo code)
- Manage active subscriptions (freeze, unfreeze, cancel)
- View current and past subscriptions, including status and usage

### 5. Notifications & Communication

- Push notifications for class reminders, subscription updates, and important events
- In-app messages for booking confirmations, cancellations, etc.

### 6. User Experience

- Modern, intuitive, and accessible UI
- Fast load times, smooth navigation
- High-contrast mode, large text support

---

## Technical Architecture

- **API Client:** Centralized module for all API calls, handling `merchant-id` and JWT tokens
- **State Management:** Redux, MobX, or Context API
- **UI Components:** Reusable, theme-aware, and accessible
- **Push Notifications:** FCM/APNS integration
- **Localization:** Arabic and English support
- **Testing:** Automated UI and integration tests

---

## Out of Scope (Phase 1)

- Payment gateway integration (use mock or manual confirmation)
- Social login
- Staff/admin features

---

## Success Metrics

- % of users completing registration and first booking
- % of booking attempts that succeed
- % of users purchasing a plan
- > 99% crash-free sessions
- 4.5+ average app store rating

---

# Milestone-Based To-Do List

---

## Milestone 1: Project Setup & Authentication

- [x] Initialize React Native project (or Flutter/Swift/Kotlin)
- [-] Set up CI/CD for mobile builds
- [-] Implement merchant selection (enter/select gym/merchant)
- [x] Implement registration screen and flow (`/api/customer-auth/register`)
- [x] Implement login screen and flow (`/api/customer-auth/login`)
- [-] Implement password reset flow (`/api/customer-auth/reset-password`)
- [x] Securely store JWT tokens and manage session state

---

## Milestone 2: Profile Management

- [ ] Implement profile screen (view profile via `/api/customer-auth/profile`)
- [ ] Implement edit profile functionality (update via `/api/customer-auth/profile`)
- [ ] Implement profile image upload/change
- [ ] Implement localization (Arabic/English, RTL/LTR)

---

## Milestone 3: Class Discovery & Booking

- [ ] Implement class list screen (fetch via `/api/public/classes`)
- [ ] Implement class filters (branch, date, trainer, difficulty, type)
- [ ] Implement class details screen
- [ ] Implement booking flow (book via `/api/public/bookings`)
- [ ] Implement bookings list (fetch via `/api/public/bookings`)
- [ ] Show booking status and handle errors (full, waitlist, etc.)

---

## Milestone 4: Subscription Management

- [ ] Implement subscription plans list (fetch via `/api/public/subscription-plans`)
- [ ] Implement plan details screen
- [ ] Implement purchase subscription flow (`/api/public/subscriptions`)
- [ ] Implement manage subscription actions (freeze, unfreeze, cancel via `/api/public/subscriptions/*`)
- [ ] Implement subscription history view

---

## Milestone 5: Notifications & UX Polish

- [ ] Integrate push notifications (FCM/APNS)
- [ ] Implement in-app messages for confirmations and alerts
- [ ] Implement high-contrast mode and large text support
- [ ] Optimize performance and loading states
- [ ] Finalize navigation and accessibility

---

## Milestone 6: Testing & Launch

- [ ] Write automated UI and integration tests for all critical flows
- [ ] Conduct manual QA and bug fixing
- [ ] Prepare app store assets (screenshots, descriptions)
- [ ] Deploy to App Store and Google Play

---

**Notes:**

- Use the OpenAPI schema as the single source of truth for API integration.
- Centralize merchant-id and token handling in the API client.
- Prioritize accessibility and localization from the start.
- Use mock data for payment flows if gateway is not integrated in Phase 1.

---

Let me know if you want this in a different format (Notion, Markdown file, etc.) or need further breakdowns for any milestone!
