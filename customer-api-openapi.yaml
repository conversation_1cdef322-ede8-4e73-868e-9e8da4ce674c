openapi: 3.0.3
info:
  title: Techrar Gym Management System - Customer API
  description: |
    Customer API for the Techrar Gym Management System. This API provides endpoints for customer authentication, profile management, and gym services.

    ## Key Concepts
    - **Merchant-Isolated Accounts** - Each merchant has completely separate customer accounts
    - **No Shared Logins** - Customers must register separately for each merchant
    - **Phone Uniqueness** - Phone numbers are unique within each merchant only

    ## Authentication Flow
    1. **Register** - Create a new customer account for a specific merchant (merchant-id sent via header)
    2. **Login** - Authenticate with email + password + merchant-id header
    3. **Profile Management** - View and update profile information
    4. **Gym Services** - Browse classes and create bookings

    ## API Headers
    **Important**: All customer authentication and public endpoints require the `merchant-id` header:
    ```
    merchant-id: 550e8400-e29b-41d4-a716-************
    ```
    This replaces the previous method of sending merchant_id in query parameters or request body.
  version: 1.0.0
  contact:
    name: Techrar Gym API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.techrar-gym.com
    description: Production server
  - url: http://localhost:3000
    description: Development server

paths:
  # Authentication Endpoints
  /api/customer-auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new customer
      description: |
        Create a new customer account. For security reasons, the same response is returned whether the email exists or not to prevent email enumeration attacks.
      operationId: registerCustomer
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID for this registration
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerRegisterRequest"
            examples:
              new_customer:
                summary: New customer registration
                value:
                  full_name: "أحمد الحازمي"
                  email: "<EMAIL>"
                  password: "securePassword123"
                  phone: "+************"
                  gender: "male"
                  date_of_birth: "1990-01-15"
      responses:
        "201":
          description: Registration request received
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegistrationResponse"
        "400":
          description: Validation error or no available gym
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/customer-auth/login:
    post:
      tags:
        - Authentication
      summary: Customer login
      description: Authenticate customer and return session tokens with subscription information
      operationId: loginCustomer
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID to login to
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerLoginRequest"
            examples:
              login_example:
                summary: Customer login
                value:
                  email: "<EMAIL>"
                  password: "securePassword123"
      responses:
        "200":
          description: Login successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerLoginResponse"
        "401":
          description: Invalid credentials or email not confirmed
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Access denied - not a customer account
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/customer-auth/logout:
    post:
      tags:
        - Authentication
      summary: Customer logout
      description: Sign out the customer and invalidate session
      operationId: logoutCustomer
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessResponse"
        "401":
          description: Not authenticated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/customer-auth/reset-password:
    post:
      tags:
        - Authentication
      summary: Request password reset
      description: Send password reset email to customer
      operationId: resetPassword
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID for password reset validation
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PasswordResetRequest"
            examples:
              reset_request:
                summary: Password reset request
                value:
                  email: "<EMAIL>"
      responses:
        "200":
          description: Reset email sent (or would be sent if account exists)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessResponse"
        "400":
          description: Validation error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Profile Management
  /api/customer-auth/profile:
    get:
      tags:
        - Profile
      summary: Get customer profile
      description: Retrieve the authenticated customer's profile information
      operationId: getCustomerProfile
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerProfileResponse"
        "401":
          description: Not authenticated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Access denied
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

    put:
      tags:
        - Profile
      summary: Update customer profile
      description: Update the authenticated customer's profile information
      operationId: updateCustomerProfile
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerProfileUpdateRequest"
            examples:
              profile_update:
                summary: Update profile
                value:
                  full_name: "أحمد محمد الحازمي"
                  phone: "+************"
                  gender: "male"
                  date_of_birth: "1990-01-15"
                  profile_image_url: "https://example.com/profile.jpg"
      responses:
        "200":
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerProfileUpdateResponse"
        "400":
          description: Validation error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: Not authenticated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Public API - Classes
  /api/public/classes:
    get:
      tags:
        - Public Services
      summary: Get available classes
      description: Retrieve class schedule for a specific gym/merchant
      operationId: getPublicClasses
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
        - name: branch_id
          in: query
          required: false
          description: Filter by specific branch
          schema:
            type: string
            format: uuid
        - name: from
          in: query
          required: false
          description: Start date/time for class search (ISO 8601)
          schema:
            type: string
            format: date-time
        - name: to
          in: query
          required: false
          description: End date/time for class search (ISO 8601)
          schema:
            type: string
            format: date-time
        - name: trainer_id
          in: query
          required: false
          description: Filter by trainer
          schema:
            type: string
            format: uuid
        - name: difficulty
          in: query
          required: false
          description: Filter by difficulty level
          schema:
            type: string
            enum: [beginner, intermediate, advanced]
        - name: is_personal_training
          in: query
          required: false
          description: Filter personal training sessions
          schema:
            type: boolean
      responses:
        "200":
          description: Classes retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ClassesResponse"
        "400":
          description: Missing required parameters
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Public API - Subscription Plans
  /api/public/subscription-plans:
    get:
      tags:
        - Subscription Management
      summary: Get available subscription plans
      description: Retrieve subscription plans available for customer purchase at a specific gym/merchant
      operationId: getPublicSubscriptionPlans
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
        - name: branch_id
          in: query
          required: false
          description: Filter by specific branch
          schema:
            type: string
            format: uuid
        - name: is_active
          in: query
          required: false
          description: Filter by active plans only
          schema:
            type: boolean
            default: true
      responses:
        "200":
          description: Subscription plans retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubscriptionPlansResponse"
        "400":
          description: Missing required parameters
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/public/subscription-plans/{id}:
    get:
      tags:
        - Subscription Management
      summary: Get specific subscription plan details
      description: Retrieve detailed information about a specific subscription plan
      operationId: getSubscriptionPlanDetails
      parameters:
        - name: id
          in: path
          required: true
          description: Subscription plan ID
          schema:
            type: string
            format: uuid
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Subscription plan details retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubscriptionPlanDetailResponse"
        "400":
          description: Missing required parameters
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Subscription plan not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Public API - Customer Subscriptions
  /api/public/subscriptions:
    get:
      tags:
        - Subscription Management
      summary: Get customer subscriptions
      description: Retrieve subscription information for an authenticated customer
      operationId: getCustomerSubscriptions
      security:
        - BearerAuth: []
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          required: false
          description: Filter by subscription status
          schema:
            type: string
            enum: [active, frozen, cancelled, expired]
        - name: include_history
          in: query
          required: false
          description: Include subscription history
          schema:
            type: boolean
            default: false
      responses:
        "200":
          description: Customer subscriptions retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PublicSubscriptionsResponse"
        "401":
          description: Not authenticated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

    post:
      tags:
        - Subscription Management
      summary: Purchase a subscription plan
      description: Purchase a subscription plan for the authenticated customer
      operationId: purchaseSubscriptionPlan
      security:
        - BearerAuth: []
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PublicPurchaseSubscriptionRequest"
            examples:
              purchase_request:
                summary: Purchase subscription
                value:
                  plan_id: "550e8400-e29b-41d4-a716-************"
                  payment_method: "credit_card"
                  auto_renew: true
                  promo_code: "NEWMEMBER20"
                  notes: "Purchased via mobile app"
      responses:
        "201":
          description: Subscription purchased successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PublicSubscriptionPurchaseResponse"
        "400":
          description: Validation error or plan not available
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: Not authenticated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "409":
          description: Customer already has active subscription
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/public/subscriptions/freeze:
    post:
      tags:
        - Subscription Management
      summary: Freeze customer subscription
      description: Freeze the customer's active subscription temporarily
      operationId: freezeCustomerSubscription
      security:
        - BearerAuth: []
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PublicFreezeSubscriptionRequest"
            examples:
              freeze_request:
                summary: Freeze subscription
                value:
                  subscription_id: "550e8400-e29b-41d4-a716-************"
                  freeze_reason: "Going on vacation for 2 weeks"
                  freeze_days: 14
      responses:
        "200":
          description: Subscription frozen successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PublicSubscriptionFreezeResponse"
        "400":
          description: Cannot freeze subscription (no credits left, etc.)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: Not authenticated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: No active subscription found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/public/subscriptions/unfreeze:
    post:
      tags:
        - Subscription Management
      summary: Unfreeze customer subscription
      description: Unfreeze the customer's frozen subscription
      operationId: unfreezeCustomerSubscription
      security:
        - BearerAuth: []
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PublicUnfreezeSubscriptionRequest"
            examples:
              unfreeze_request:
                summary: Unfreeze subscription
                value:
                  subscription_id: "550e8400-e29b-41d4-a716-************"
                  unfreeze_reason: "Vacation ended, ready to resume workouts"
      responses:
        "200":
          description: Subscription unfrozen successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PublicSubscriptionUnfreezeResponse"
        "400":
          description: Cannot unfreeze subscription
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: Not authenticated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: No frozen subscription found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/public/subscriptions/cancel:
    post:
      tags:
        - Subscription Management
      summary: Cancel customer subscription
      description: Cancel the customer's active subscription
      operationId: cancelCustomerSubscription
      security:
        - BearerAuth: []
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PublicCancelSubscriptionRequest"
            examples:
              cancel_request:
                summary: Cancel subscription
                value:
                  subscription_id: "550e8400-e29b-41d4-a716-************"
                  cancellation_reason: "Moving to another city"
                  cancellation_date: "2024-02-01T00:00"
      responses:
        "200":
          description: Subscription cancellation request submitted
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PublicSubscriptionCancelResponse"
        "400":
          description: Cannot cancel subscription
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: Not authenticated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: No active subscription found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Public API - Bookings
  /api/public/bookings:
    post:
      tags:
        - Public Services
      summary: Create a booking
      description: Book a class session for a member
      operationId: createPublicBooking
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateBookingRequest"
            examples:
              booking_request:
                summary: Create booking
                value:
                  member_id: "550e8400-e29b-41d4-a716-************"
                  session_id: "550e8400-e29b-41d4-a716-************"
                  booking_notes: "First time attending this class"
      responses:
        "201":
          description: Booking created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BookingResponse"
        "400":
          description: Validation error, class full, or booking restrictions
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BookingErrorResponse"
        "404":
          description: Member or session not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

    get:
      tags:
        - Public Services
      summary: Get member bookings
      description: Retrieve bookings for a specific member
      operationId: getMemberBookings
      parameters:
        - name: merchant-id
          in: header
          required: true
          description: The gym/merchant ID
          schema:
            type: string
            format: uuid
        - name: member_id
          in: query
          required: true
          description: The member ID
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          required: false
          description: Filter by booking status
          schema:
            type: string
            enum: [confirmed, cancelled, waitlisted, no_show]
        - name: timeframe
          in: query
          required: false
          description: Filter by time period
          schema:
            type: string
            enum: [upcoming, past]
            default: upcoming
      responses:
        "200":
          description: Bookings retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemberBookingsResponse"
        "400":
          description: Missing required parameters
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Member not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Supabase JWT token obtained from login

  schemas:
    # Request Schemas
    CustomerRegisterRequest:
      type: object
      required:
        - full_name
        - email
        - password
      properties:
        full_name:
          type: string
          minLength: 2
          maxLength: 100
          description: Customer's full name (Arabic or English)
          example: "أحمد الحازمي"
        email:
          type: string
          format: email
          description: Customer's email address
          example: "<EMAIL>"
        password:
          type: string
          minLength: 6
          maxLength: 128
          description: Customer's password
          example: "securePassword123"
        phone:
          type: string
          pattern: '^(\+966|966|0)?(5[0-9]|1[1-9]|2[1-9]|3[1-9]|4[1-9]|7[1-9])[0-9]{7}$'
          description: Saudi phone number
          example: "+************"
        gender:
          type: string
          enum: [male, female, other]
          description: Customer's gender
          example: "male"
        date_of_birth:
          type: string
          format: date
          description: Customer's date of birth
          example: "1990-01-15"

    CustomerLoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          description: Customer's email address
          example: "<EMAIL>"
        password:
          type: string
          description: Customer's password
          example: "securePassword123"

    PasswordResetRequest:
      type: object
      required:
        - email
      properties:
        email:
          type: string
          format: email
          description: Customer's email address
          example: "<EMAIL>"

    CustomerProfileUpdateRequest:
      type: object
      properties:
        full_name:
          type: string
          minLength: 2
          maxLength: 100
          description: Customer's full name
          example: "أحمد محمد الحازمي"
        phone:
          type: string
          pattern: '^(\+966|966|0)?(5[0-9]|1[1-9]|2[1-9]|3[1-9]|4[1-9]|7[1-9])[0-9]{7}$'
          description: Saudi phone number
          example: "+************"
        gender:
          type: string
          enum: [male, female, other]
          description: Customer's gender
          example: "male"
        date_of_birth:
          type: string
          format: date
          description: Customer's date of birth
          example: "1990-01-15"
        profile_image_url:
          type: string
          format: uri
          description: URL to customer's profile image
          example: "https://example.com/profile.jpg"

    CreateBookingRequest:
      type: object
      required:
        - member_id
        - session_id
      properties:
        member_id:
          type: string
          format: uuid
          description: ID of the member making the booking
          example: "550e8400-e29b-41d4-a716-************"
        session_id:
          type: string
          format: uuid
          description: ID of the class session to book
          example: "550e8400-e29b-41d4-a716-************"
        booking_notes:
          type: string
          maxLength: 500
          description: Optional notes for the booking
          example: "First time attending this class"

    PurchaseSubscriptionRequest:
      type: object
      required:
        - plan_id
        - payment_method
      properties:
        plan_id:
          type: string
          format: uuid
          description: ID of the subscription plan to purchase
          example: "550e8400-e29b-41d4-a716-************"
        payment_method:
          type: string
          enum: [credit_card, debit_card, cash, bank_transfer]
          description: Payment method for the subscription
          example: "credit_card"
        auto_renew:
          type: boolean
          description: Enable automatic renewal
          default: true
          example: true
        promo_code:
          type: string
          description: Optional promotional code
          example: "NEWMEMBER20"

    FreezeSubscriptionRequest:
      type: object
      required:
        - reason
        - days
      properties:
        reason:
          type: string
          enum: [vacation, medical, personal, financial, other]
          description: Reason for freezing subscription
          example: "vacation"
        days:
          type: integer
          minimum: 1
          maximum: 90
          description: Number of days to freeze
          example: 14
        note:
          type: string
          maxLength: 200
          description: Optional note about the freeze
          example: "Going on vacation for 2 weeks"

    CancelSubscriptionRequest:
      type: object
      required:
        - reason
      properties:
        reason:
          type: string
          enum: [relocating, financial, unsatisfied, medical, schedule, other]
          description: Reason for cancelling subscription
          example: "relocating"
        feedback:
          type: string
          maxLength: 500
          description: Optional feedback about the cancellation
          example: "Moving to another city"
        immediate:
          type: boolean
          description: Cancel immediately or at end of current period
          default: false
          example: false

    # Public API Request Schemas
    PublicFreezeSubscriptionRequest:
      type: object
      required:
        - subscription_id
        - freeze_reason
        - freeze_days
      properties:
        subscription_id:
          type: string
          format: uuid
          description: ID of the subscription to freeze
          example: "550e8400-e29b-41d4-a716-************"
        freeze_reason:
          type: string
          minLength: 1
          maxLength: 500
          description: Reason for freezing subscription
          example: "Going on vacation for 2 weeks"
        freeze_days:
          type: integer
          minimum: 1
          maximum: 365
          description: Number of days to freeze
          example: 14
        freeze_start_date:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$'
          description: Optional freeze start date (ISO format)
          example: "2024-02-01T00:00"

    PublicUnfreezeSubscriptionRequest:
      type: object
      required:
        - subscription_id
      properties:
        subscription_id:
          type: string
          format: uuid
          description: ID of the subscription to unfreeze
          example: "550e8400-e29b-41d4-a716-************"
        unfreeze_reason:
          type: string
          maxLength: 500
          description: Optional reason for unfreezing
          example: "Vacation ended, ready to resume workouts"

    PublicCancelSubscriptionRequest:
      type: object
      required:
        - subscription_id
        - cancellation_reason
      properties:
        subscription_id:
          type: string
          format: uuid
          description: ID of the subscription to cancel
          example: "550e8400-e29b-41d4-a716-************"
        cancellation_reason:
          type: string
          minLength: 1
          maxLength: 500
          description: Reason for cancelling subscription
          example: "Moving to another city"
        cancellation_date:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$'
          description: Optional cancellation date (ISO format)
          example: "2024-02-01T00:00"

    PublicPurchaseSubscriptionRequest:
      type: object
      required:
        - plan_id
        - payment_method
      properties:
        plan_id:
          type: string
          format: uuid
          description: ID of the subscription plan to purchase
          example: "550e8400-e29b-41d4-a716-************"
        payment_method:
          type: string
          minLength: 1
          maxLength: 50
          description: Payment method (credit_card, debit_card, cash, etc.)
          example: "credit_card"
        auto_renew:
          type: boolean
          default: true
          description: Enable automatic renewal
          example: true
        promo_code:
          type: string
          maxLength: 50
          description: Optional promotional code
          example: "NEWMEMBER20"
        start_date:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$'
          description: Optional subscription start date (ISO format)
          example: "2024-02-01T00:00"
        notes:
          type: string
          maxLength: 500
          description: Optional notes for the subscription
          example: "Purchased via mobile app"

    # Response Schemas
    RegistrationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Registration request received. Please check your email for next steps."
        data:
          type: object
          properties:
            requires_email_confirmation:
              type: boolean
              example: true

    CustomerLoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Login successful"
        data:
          type: object
          properties:
            customer:
              $ref: "#/components/schemas/CustomerProfile"
            session:
              $ref: "#/components/schemas/Session"
            subscriptions:
              type: array
              items:
                $ref: "#/components/schemas/Subscription"
            total_subscriptions:
              type: integer
              example: 2
            active_subscriptions:
              type: integer
              example: 1

    CustomerProfileResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Profile retrieved successfully"
        data:
          type: object
          properties:
            customer:
              $ref: "#/components/schemas/CustomerProfile"

    CustomerProfileUpdateResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Profile updated successfully"
        data:
          type: object
          properties:
            customer:
              $ref: "#/components/schemas/CustomerProfile"

    ClassesResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Classes retrieved successfully"
        data:
          type: object
          properties:
            classes:
              type: array
              items:
                $ref: "#/components/schemas/ClassWithSessions"
            summary:
              type: object
              properties:
                total_classes:
                  type: integer
                  example: 15
                total_sessions:
                  type: integer
                  example: 45
        errors:
          nullable: true

    BookingResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Booking confirmed successfully!"
        data:
          type: object
          properties:
            booking:
              $ref: "#/components/schemas/Booking"
            booking_details:
              type: object
              properties:
                class_name:
                  type: string
                  example: "Morning Yoga"
                session_time:
                  type: string
                  format: date-time
                  example: "2024-01-15T08:00:00Z"
                trainer:
                  type: string
                  example: "Sarah Ahmed"
                room:
                  type: string
                  example: "Studio A"
        errors:
          nullable: true

    MemberBookingsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Bookings retrieved successfully"
        data:
          type: object
          properties:
            bookings:
              type: array
              items:
                $ref: "#/components/schemas/BookingWithDetails"
            count:
              type: integer
              example: 5
        errors:
          nullable: true

    SubscriptionPlansResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscription plans fetched successfully"
        data:
          type: array
          items:
            $ref: "#/components/schemas/SubscriptionPlanPublic"
        errors:
          nullable: true

    SubscriptionPlanDetailResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscription plan details fetched successfully"
        data:
          $ref: "#/components/schemas/SubscriptionPlanPublic"
        errors:
          nullable: true

    CustomerSubscriptionsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/SubscriptionWithDetails"
        active_subscription:
          $ref: "#/components/schemas/SubscriptionWithDetails"
        summary:
          type: object
          properties:
            total_subscriptions:
              type: integer
              example: 2
            active_subscriptions:
              type: integer
              example: 1
            frozen_subscriptions:
              type: integer
              example: 0

    PublicSubscriptionsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscriptions retrieved successfully"
        data:
          type: object
          properties:
            subscriptions:
              type: array
              items:
                $ref: "#/components/schemas/PublicSubscriptionDetails"
            summary:
              type: object
              properties:
                total:
                  type: integer
                  example: 3
                active:
                  type: integer
                  example: 1
                frozen:
                  type: integer
                  example: 1
                expired:
                  type: integer
                  example: 1
                cancelled:
                  type: integer
                  example: 0
        errors:
          nullable: true

    SubscriptionPurchaseResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscription purchased successfully"
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/SubscriptionWithDetails"
            payment:
              type: object
              properties:
                amount:
                  type: number
                  format: decimal
                  example: 299.99
                currency:
                  type: string
                  example: "SAR"
                payment_method:
                  type: string
                  example: "credit_card"
                transaction_id:
                  type: string
                  example: "txn_123456789"

    SubscriptionActionResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscription frozen successfully"
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/SubscriptionWithDetails"
            effective_date:
              type: string
              format: date-time
              example: "2024-01-15T00:00:00Z"

    SubscriptionCancellationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscription cancellation processed"
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/SubscriptionWithDetails"
            cancellation_date:
              type: string
              format: date-time
              example: "2024-01-31T23:59:59Z"
            refund_amount:
              type: number
              format: decimal
              example: 149.99
            refund_method:
              type: string
              example: "credit_card"

    # Public API Response Schemas
    PublicSubscriptionFreezeResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscription frozen successfully for 14 days"
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/PublicSubscriptionDetails"
            freeze_details:
              type: object
              properties:
                freeze_reason:
                  type: string
                  example: "Going on vacation for 2 weeks"
                freeze_days:
                  type: integer
                  example: 14
                freeze_start_date:
                  type: string
                  format: date-time
                  example: "2024-02-01T00:00:00Z"
                new_end_date:
                  type: string
                  format: date-time
                  example: "2024-03-15T23:59:59Z"
                remaining_freeze_days:
                  type: integer
                  example: 16
                remaining_freeze_attempts:
                  type: integer
                  example: 1
        errors:
          nullable: true

    PublicSubscriptionUnfreezeResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscription unfrozen successfully"
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/PublicSubscriptionDetails"
            unfreeze_details:
              type: object
              properties:
                unfreeze_date:
                  type: string
                  format: date-time
                  example: "2024-02-15T00:00:00Z"
                unfreeze_reason:
                  type: string
                  example: "Vacation ended, ready to resume workouts"
                remaining_days:
                  type: integer
                  example: 28
                new_status:
                  type: string
                  example: "active"
        errors:
          nullable: true

    PublicSubscriptionCancelResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Cancellation request submitted successfully. Gym staff will review your request."
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/PublicSubscriptionDetails"
            cancellation_details:
              type: object
              properties:
                cancellation_reason:
                  type: string
                  example: "Moving to another city"
                request_date:
                  type: string
                  format: date-time
                  example: "2024-02-01T00:00:00Z"
                status:
                  type: string
                  example: "pending_review"
                remaining_days:
                  type: integer
                  example: 28
                potential_refund:
                  type: number
                  format: decimal
                  example: 149.99
                next_steps:
                  type: string
                  example: "Your cancellation request is pending approval. You will be contacted by gym staff within 24-48 hours."
        errors:
          nullable: true

    PublicSubscriptionPurchaseResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscription purchased successfully!"
        data:
          type: object
          properties:
            subscription:
              $ref: "#/components/schemas/PublicSubscriptionDetails"
            payment:
              type: object
              properties:
                amount:
                  type: number
                  format: decimal
                  example: 299.99
                currency:
                  type: string
                  example: "SAR"
                payment_method:
                  type: string
                  example: "credit_card"
                status:
                  type: string
                  example: "completed"
                transaction_id:
                  type: string
                  example: "mock_txn_1708123456789"
                processed_at:
                  type: string
                  format: date-time
                  example: "2024-02-01T10:30:00Z"
            welcome_message:
              type: string
              example: "Welcome to Basic Monthly Plan! Your subscription is now active and will expire on 2024-03-01."
            next_steps:
              type: array
              items:
                type: string
              example:
                [
                  "You can now book classes through the app",
                  "Check your subscription details in the 'My Subscriptions' section",
                  "Download the gym app for easy check-ins",
                ]
        errors:
          nullable: true

    BookingErrorResponse:
      type: object
      properties:
        error:
          type: string
          example: "Class is full"
        can_join_waitlist:
          type: boolean
          example: true
        available_spots:
          type: integer
          example: 0
        capacity:
          type: integer
          example: 20
        limit:
          type: integer
          example: 3
        current_count:
          type: integer
          example: 3

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"
        data:
          nullable: true

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "An error occurred"
        errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                example: "email"
              message:
                type: string
                example: "Email is required"

    # Entity Schemas
    Customer:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        full_name:
          type: string
          example: "أحمد الحازمي"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+************"
        gender:
          type: string
          enum: [male, female, other]
          example: "male"
        date_of_birth:
          type: string
          format: date
          example: "1990-01-15"

    CustomerProfile:
      allOf:
        - $ref: "#/components/schemas/Customer"
        - type: object
          properties:
            user_id:
              type: string
              format: uuid
              example: "550e8400-e29b-41d4-a716-************"
            profile_image_url:
              type: string
              format: uri
              example: "https://example.com/profile.jpg"
            gym_id:
              type: string
              format: uuid
              example: "550e8400-e29b-41d4-a716-************"
            merchant_id:
              type: string
              format: uuid
              example: "550e8400-e29b-41d4-a716-************"
            status:
              type: string
              enum: [active, inactive, suspended]
              example: "active"
            is_active:
              type: boolean
              example: true
            created_at:
              type: string
              format: date-time
              example: "2024-01-01T00:00:00Z"

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        email:
          type: string
          format: email
          example: "<EMAIL>"

    Session:
      type: object
      properties:
        access_token:
          type: string
          description: JWT access token
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        refresh_token:
          type: string
          description: JWT refresh token
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        expires_at:
          type: integer
          description: Token expiration timestamp
          example: 1736985600

    Subscription:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        status:
          type: string
          enum: [active, expired, cancelled, frozen]
          example: "active"
        subscription_plans:
          $ref: "#/components/schemas/SubscriptionPlan"

    SubscriptionPlan:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          example: "Basic Monthly Plan"
        price:
          type: number
          format: decimal
          example: 299.99
        duration:
          type: integer
          description: Duration in days
          example: 30

    SubscriptionPlanPublic:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          example: "Basic Monthly Plan"
        description:
          type: string
          example: "Perfect for beginners with access to all basic gym facilities"
        price:
          type: number
          format: decimal
          example: 299.99
        currency:
          type: string
          example: "SAR"
        duration:
          type: integer
          description: Duration in days
          example: 30
        duration_type:
          type: string
          enum: [days, weeks, months, years]
          example: "months"
        classes_per_week:
          type: integer
          description: Number of classes allowed per week
          example: 3
        freeze_credits:
          type: integer
          description: Number of times subscription can be frozen
          example: 2
        features:
          type: array
          items:
            type: string
          example: ["Gym Access", "Group Classes", "Locker Access"]
        is_popular:
          type: boolean
          description: Whether this plan is marked as popular
          example: false
        branch:
          $ref: "#/components/schemas/Branch"

    SubscriptionWithDetails:
      allOf:
        - $ref: "#/components/schemas/Subscription"
        - type: object
          properties:
            start_date:
              type: string
              format: date
              example: "2024-01-01"
            end_date:
              type: string
              format: date
              example: "2024-01-31"
            days_remaining:
              type: integer
              description: Days remaining in subscription
              example: 15
            freeze_days_used:
              type: integer
              description: Number of freeze days used
              example: 5
            freeze_attempts_used:
              type: integer
              description: Number of freeze attempts used
              example: 1
            classes_used_this_week:
              type: integer
              description: Classes attended this week
              example: 2
            subscription_plans:
              $ref: "#/components/schemas/SubscriptionPlanPublic"
            member:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                  example: "550e8400-e29b-41d4-a716-************"
                full_name:
                  type: string
                  example: "أحمد الحازمي"

    PublicSubscriptionDetails:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        status:
          type: string
          enum: [active, expired, cancelled, frozen, pending]
          example: "active"
        start_date:
          type: string
          format: date-time
          example: "2024-01-01T00:00:00Z"
        end_date:
          type: string
          format: date-time
          example: "2024-01-31T23:59:59Z"
        is_expired:
          type: boolean
          example: false
        days_remaining:
          type: integer
          description: Days remaining in subscription
          example: 15
        is_currently_active:
          type: boolean
          example: true
        can_freeze:
          type: boolean
          description: Whether subscription can be frozen
          example: true
        can_unfreeze:
          type: boolean
          description: Whether subscription can be unfrozen
          example: false
        freeze_days_credit:
          type: integer
          description: Remaining freeze days available
          example: 30
        freeze_attempts_credit:
          type: integer
          description: Remaining freeze attempts available
          example: 2
        auto_renew:
          type: boolean
          example: true
        plan:
          $ref: "#/components/schemas/SubscriptionPlanPublic"
        created_at:
          type: string
          format: date-time
          example: "2024-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2024-01-15T12:00:00Z"

    Gym:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          example: "Downtown Fitness"

    ClassWithSessions:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          example: "Morning Yoga"
        description:
          type: string
          example: "Relaxing yoga session to start your day"
        capacity:
          type: integer
          example: 20
        duration:
          type: integer
          description: Duration in minutes
          example: 60
        price:
          type: number
          format: decimal
          example: 50.00
        difficulty:
          type: string
          enum: [beginner, intermediate, advanced]
          example: "beginner"
        is_personal_training:
          type: boolean
          example: false
        room:
          type: string
          example: "Studio A"
        trainer:
          $ref: "#/components/schemas/Trainer"
        branch:
          $ref: "#/components/schemas/Branch"
        sessions:
          type: array
          items:
            $ref: "#/components/schemas/Session"

    Trainer:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        full_name:
          type: string
          example: "Sarah Ahmed"
        profile_image_url:
          type: string
          format: uri
          example: "https://example.com/trainer.jpg"
        specializations:
          type: array
          items:
            type: string
          example: ["Yoga", "Pilates"]

    Branch:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          example: "Downtown Branch"
        address:
          type: string
          example: "123 King Fahd Road, Riyadh"

    ClassSession:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        class_id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        start_time:
          type: string
          format: date-time
          example: "2024-01-15T08:00:00Z"
        end_time:
          type: string
          format: date-time
          example: "2024-01-15T09:00:00Z"
        status:
          type: string
          enum: [scheduled, in_progress, completed, cancelled]
          example: "scheduled"
        booked_count:
          type: integer
          example: 15
        location:
          type: string
          example: "Studio A"
        is_cancelled:
          type: boolean
          example: false
        available_spots:
          type: integer
          description: Calculated field (capacity - booked_count)
          example: 5
        is_full:
          type: boolean
          description: Calculated field (booked_count >= capacity)
          example: false

    Booking:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        merchant_id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        member_id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        session_id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        booking_date:
          type: string
          format: date-time
          example: "2024-01-10T12:00:00Z"
        status:
          type: string
          enum: [confirmed, cancelled, waitlisted, no_show]
          example: "confirmed"
        booking_notes:
          type: string
          example: "First time attending this class"
        created_by:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        updated_by:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"

    BookingWithDetails:
      allOf:
        - $ref: "#/components/schemas/Booking"
        - type: object
          properties:
            session:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                  example: "550e8400-e29b-41d4-a716-************"
                start_time:
                  type: string
                  format: date-time
                  example: "2024-01-15T08:00:00Z"
                end_time:
                  type: string
                  format: date-time
                  example: "2024-01-15T09:00:00Z"
                session_status:
                  type: string
                  enum: [scheduled, in_progress, completed, cancelled]
                  example: "scheduled"
                location:
                  type: string
                  example: "Studio A"
                is_cancelled:
                  type: boolean
                  example: false
                class:
                  type: object
                  properties:
                    id:
                      type: string
                      format: uuid
                      example: "550e8400-e29b-41d4-a716-************"
                    name:
                      type: string
                      example: "Morning Yoga"
                    description:
                      type: string
                      example: "Relaxing yoga session to start your day"
                    duration:
                      type: integer
                      example: 60
                    room:
                      type: string
                      example: "Studio A"
                    difficulty:
                      type: string
                      enum: [beginner, intermediate, advanced]
                      example: "beginner"
                    trainer:
                      type: object
                      properties:
                        full_name:
                          type: string
                          example: "Sarah Ahmed"
                        profile_image_url:
                          type: string
                          format: uri
                          example: "https://example.com/trainer.jpg"

tags:
  - name: Authentication
    description: Customer authentication endpoints
  - name: Profile
    description: Customer profile management
  - name: Subscription Management
    description: Customer subscription management endpoints
  - name: Public Services
    description: Public API endpoints for gym services
