{"cSpell.ignoreRegExpList": ["/[\\u0621-\\u064A]+/"], "files.exclude": {"**/**.freezed.dart": true, "**/**.g.dart": true}, "dart.lineLength": 120, "[yaml]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "recentlyUsed", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}, "[sh]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "recentlyUsed", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}, "[bash]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "recentlyUsed", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}, "[dart]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [120], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "recentlyUsed", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}, "yaml.schemaStore.enable": false, "java.configuration.updateBuildConfiguration": "disabled"}