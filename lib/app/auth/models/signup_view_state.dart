class SignupViewState {
  final String fullName;
  final String email;
  final String password;
  final String phone;
  final String gender;
  final DateTime? dateOfBirth;
  final String? fullNameError;
  final String? emailError;
  final String? passwordError;
  final String? phoneError;
  final String? dateOfBirthError;
  final String? generalError;
  final bool obscurePassword;
  final bool isLoading;
  final bool isSuccess;

  SignupViewState({
    this.fullName = '',
    this.email = '',
    this.password = '',
    this.phone = '',
    this.gender = 'male',
    this.dateOfBirth,
    this.fullNameError,
    this.emailError,
    this.passwordError,
    this.phoneError,
    this.dateOfBirthError,
    this.generalError,
    this.obscurePassword = true,
    this.isLoading = false,
    this.isSuccess = false,
  });

  SignupViewState copyWith({
    String? fullName,
    String? email,
    String? password,
    String? phone,
    String? gender,
    DateTime? dateOfBirth,
    String? fullNameError,
    String? emailError,
    String? passwordError, 
    String? phoneError,
    String? dateOfBirthError,
    String? generalError,
    bool? obscurePassword,
    bool? isLoading,
    bool? isSuccess,
  }) {
    return SignupViewState(
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      password: password ?? this.password,
      phone: phone ?? this.phone,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      fullNameError: fullNameError,
      emailError: emailError,
      passwordError: passwordError,
      phoneError: phoneError,
      dateOfBirthError: dateOfBirthError,
      generalError: generalError,
      obscurePassword: obscurePassword ?? this.obscurePassword,
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }

  bool get isValid =>
      fullName.isNotEmpty &&
      email.isNotEmpty &&
      password.isNotEmpty &&
      phone.isNotEmpty &&
      fullNameError == null &&
      emailError == null &&
      passwordError == null &&
      phoneError == null;
}
