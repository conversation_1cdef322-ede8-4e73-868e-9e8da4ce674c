class Customer {
  final String id;
  final String fullName;
  final String email;
  final String phone;
  final String? profileImageUrl;
  final String gymId;
  final String status;
  final bool isActive;
  final String? gender;
  final DateTime? dateOfBirth;

  Customer({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    this.profileImageUrl,
    required this.gymId,
    required this.status,
    required this.isActive,
    this.gender,
    this.dateOfBirth,
  });

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'],
      fullName: json['full_name'],
      email: json['email'],
      phone: json['phone'],
      profileImageUrl: json['profile_image_url'],
      gymId: json['gym_id'] ?? '-',
      status: json['status'],
      isActive: json['is_active'] ?? true,
      gender: json['gender'],
      dateOfBirth: json['date_of_birth'] != null ? DateTime.parse(json['date_of_birth']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'profile_image_url': profileImageUrl,
      'gym_id': gymId,
      'status': status,
      'is_active': isActive,
      'gender': gender,
      'date_of_birth': dateOfBirth?.toIso8601String(),
    };
  }
}
