class LoginViewState {
  final String email;
  final String password;
  final String? emailError;
  final String? passwordError;
  final String? generalError;
  final bool isLoading;
  final bool obscurePassword;

  LoginViewState({
    required this.email,
    required this.password,
    this.emailError,
    this.passwordError,
    this.generalError,
    this.isLoading = false,
    this.obscurePassword = true,
  });

  LoginViewState copyWith({
    String? email,
    String? password,
    String? emailError,
    String? passwordError,
    String? generalError,
    bool? isLoading,
    bool? obscurePassword,
  }) {
    return LoginViewState(
      email: email ?? this.email,
      password: password ?? this.password,
      emailError: emailError,
      passwordError: passwordError,
      generalError: generalError,
      isLoading: isLoading ?? this.isLoading,
      obscurePassword: obscurePassword ?? this.obscurePassword,
    );
  }

  bool get isValid => email.isNotEmpty && password.isNotEmpty && emailError == null && passwordError == null;
}
