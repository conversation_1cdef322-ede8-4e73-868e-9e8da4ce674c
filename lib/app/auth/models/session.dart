class Session {
  final String accessToken;
  final String refreshToken;
  final int expiresAt;

  Session({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
  });

  factory Session.fromJson(Map<String, dynamic> json) {
    return Session(
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      expiresAt: json['expires_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'expires_at': expiresAt,
    };
  }

  bool get isExpired {
    return DateTime.now().millisecondsSinceEpoch > expiresAt * 1000;
  }
}