import '../../subscriptions/models/subscription.dart';
import 'customer.dart';
import 'session.dart';

class AuthResponse {
  final Customer customer;
  final Session session;
  final List<Subscription>? subscriptions;
  final int? totalSubscriptions;
  final int? activeSubscriptions;

  AuthResponse({
    required this.customer,
    required this.session,
    this.subscriptions,
    this.totalSubscriptions,
    this.activeSubscriptions,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      customer: Customer.fromJson(json['customer']),
      session: Session.fromJson(json['session']),
      subscriptions: json['subscriptions'] != null
          ? (json['subscriptions'] as List).map((s) => Subscription.fromJson(s)).toList()
          : null,
      totalSubscriptions: json['total_subscriptions'],
      activeSubscriptions: json['active_subscriptions'],
    );
  }
}
