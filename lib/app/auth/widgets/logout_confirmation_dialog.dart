import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/app/auth/providers/auth_provider.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';

class LogoutConfirmationDialog extends ConsumerStatefulWidget {
  const LogoutConfirmationDialog({super.key});

  @override
  ConsumerState<LogoutConfirmationDialog> createState() => _LogoutConfirmationDialogState();
}

class _LogoutConfirmationDialogState extends ConsumerState<LogoutConfirmationDialog> {
  bool _isLoggingOut = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.surfaceColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
      ),
      title: Row(
        children: [
          Icon(
            Icons.logout_rounded,
            color: AppColors.errorColor,
            size: AppSizes.iconMd,
          ),
          const SizedBox(width: AppSpacing.sm),
          Text(
            'Logout',
            style: TextStyles.h2.copyWith(
              color: AppColors.primaryTextColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      content: Text(
        'Are you sure you want to logout? You will need to sign in again to access your account.',
        style: TextStyles.body1.copyWith(
          color: AppColors.secondaryTextColor,
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoggingOut ? null : () => Navigator.of(context).pop(false),
          child: Text(
            'Cancel',
            style: TextStyles.buttonText.copyWith(
              color: AppColors.secondaryTextColor,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: _isLoggingOut ? null : _handleLogout,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.errorColor,
            foregroundColor: AppColors.white,
            disabledBackgroundColor: AppColors.buttonDisabledColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSizes.buttonRadius),
            ),
          ),
          child: _isLoggingOut
              ? SizedBox(
                  width: AppSizes.iconSm,
                  height: AppSizes.iconSm,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                  ),
                )
              : Text(
                  'Logout',
                  style: TextStyles.buttonText,
                ),
        ),
      ],
    );
  }

  Future<void> _handleLogout() async {
    setState(() {
      _isLoggingOut = true;
    });

    try {
      await ref.read(authNotifierProvider.notifier).logout();
      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoggingOut = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to logout. Please try again.'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }
}

/// Helper function to show logout confirmation dialog
Future<bool?> showLogoutConfirmationDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => const LogoutConfirmationDialog(),
  );
}
