import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:techrar_gym/app/auth/providers/signup_provider.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';

import '../models/signup_view_state.dart';

class SignupView extends ConsumerStatefulWidget {
  static final name = 'signup-view';
  const SignupView({super.key});

  @override
  ConsumerState<SignupView> createState() => _SignupViewState();
}

class _SignupViewState extends ConsumerState<SignupView> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _dateOfBirthController = TextEditingController();

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    _dateOfBirthController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final signupNotifier = ref.read(signupNotifierProvider.notifier);
    final signupState = ref.watch(signupNotifierProvider);

    // If registration was successful, show success message and navigate back
    if (signupState.isSuccess) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Registration successful! Please check your email to confirm your account.'),
            backgroundColor: AppColors.successColor,
          ),
        );
        Navigator.of(context).pop();
      });
    }

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        title: const Text('Create Account'),
        backgroundColor: AppColors.backgroundColor,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.screenPadding),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (signupState.generalError != null)
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.sm),
                    margin: const EdgeInsets.only(bottom: AppSpacing.md),
                    decoration: BoxDecoration(
                      color: AppColors.errorColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppSizes.cardRadius),
                    ),
                    child: Text(
                      signupState.generalError!,
                      style: TextStyles.body2.copyWith(color: AppColors.errorColor),
                    ),
                  ),

                // Full Name Field
                TextFormField(
                  controller: _fullNameController,
                  textInputAction: TextInputAction.next,
                  onChanged: signupNotifier.updateFullName,
                  style: TextStyles.inputText,
                  decoration: InputDecoration(
                    labelText: 'Full Name',
                    hintText: 'Enter your full name',
                    errorText: signupState.fullNameError,
                    prefixIcon: Icon(Icons.person_outline, color: AppColors.secondaryTextColor),
                  ),
                ),
                const SizedBox(height: AppSpacing.md),

                // Email Field
                TextFormField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  onChanged: signupNotifier.updateEmail,
                  style: TextStyles.inputText,
                  decoration: InputDecoration(
                    labelText: 'Email',
                    hintText: 'Enter your email',
                    errorText: signupState.emailError,
                    prefixIcon: Icon(Icons.email_outlined, color: AppColors.secondaryTextColor),
                  ),
                ),
                const SizedBox(height: AppSpacing.md),

                // Password Field
                TextFormField(
                  controller: _passwordController,
                  obscureText: signupState.obscurePassword,
                  textInputAction: TextInputAction.next,
                  onChanged: signupNotifier.updatePassword,
                  style: TextStyles.inputText,
                  decoration: InputDecoration(
                    labelText: 'Password',
                    hintText: 'Create a password',
                    errorText: signupState.passwordError,
                    prefixIcon: Icon(Icons.lock_outline, color: AppColors.secondaryTextColor),
                    suffixIcon: IconButton(
                      icon: Icon(
                        signupState.obscurePassword ? Icons.visibility_off : Icons.visibility,
                        color: AppColors.secondaryTextColor,
                      ),
                      onPressed: signupNotifier.togglePasswordVisibility,
                    ),
                  ),
                ),
                const SizedBox(height: AppSpacing.md),

                // Phone Field
                TextFormField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  textInputAction: TextInputAction.next,
                  onChanged: signupNotifier.updatePhone,
                  style: TextStyles.inputText,
                  decoration: InputDecoration(
                    labelText: 'Phone Number',
                    hintText: 'Enter your Saudi phone number',
                    errorText: signupState.phoneError,
                    prefixIcon: Icon(Icons.phone_outlined, color: AppColors.secondaryTextColor),
                  ),
                ),
                const SizedBox(height: AppSpacing.md),

                // Gender Selection
                DropdownButtonFormField<String>(
                  value: signupState.gender,
                  decoration: InputDecoration(
                    labelText: 'Gender',
                    prefixIcon: Icon(Icons.person_outline, color: AppColors.secondaryTextColor),
                  ),
                  items: [
                    DropdownMenuItem(value: 'male', child: Text('Male')),
                    DropdownMenuItem(value: 'female', child: Text('Female')),
                    DropdownMenuItem(value: 'other', child: Text('Other')),
                  ],
                  onChanged: (value) {
                    if (value != null) signupNotifier.updateGender(value);
                  },
                ),
                const SizedBox(height: AppSpacing.md),

                // Date of Birth Field
                TextFormField(
                  controller: _dateOfBirthController,
                  readOnly: true,
                  onTap: () => _selectDate(context, signupNotifier, signupState),
                  style: TextStyles.inputText,
                  decoration: InputDecoration(
                    labelText: 'Date of Birth',
                    hintText: 'Select your date of birth',
                    errorText: signupState.dateOfBirthError,
                    prefixIcon: Icon(Icons.calendar_today, color: AppColors.secondaryTextColor),
                  ),
                ),
                const SizedBox(height: AppSpacing.xl),

                // Register Button
                SizedBox(
                  width: double.infinity,
                  height: AppSizes.buttonHeight,
                  child: ElevatedButton(
                    onPressed: signupState.isLoading ? null : () => _handleSignup(signupNotifier, signupState),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.buttonPrimaryColor,
                      foregroundColor: AppColors.buttonTextColor,
                      disabledBackgroundColor: AppColors.buttonDisabledColor,
                      elevation: AppSizes.appBarElevation,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppSizes.buttonRadius),
                      ),
                    ),
                    child: signupState.isLoading
                        ? SizedBox(
                            width: AppSizes.iconMd,
                            height: AppSizes.iconMd,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                            ),
                          )
                        : Text(
                            'Create Account',
                            style: TextStyles.buttonText,
                          ),
                  ),
                ),
                const SizedBox(height: AppSpacing.md),

                // Login Link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Already have an account? ",
                      style: TextStyles.body2.copyWith(color: AppColors.secondaryTextColor),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        'Login',
                        style: TextStyles.body2.copyWith(
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, SignupNotifier notifier, SignupViewState state) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: state.dateOfBirth ?? DateTime.now().subtract(const Duration(days: 365 * 18)),
      firstDate: DateTime(1940),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primaryColor,
              onPrimary: AppColors.white,
              onSurface: AppColors.primaryTextColor,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null) {
      notifier.updateDateOfBirth(picked);
      _dateOfBirthController.text = DateFormat('yyyy-MM-dd').format(picked);
    }
  }

  void _handleSignup(SignupNotifier notifier, SignupViewState state) {
    // Update controllers to match state if needed
    if (_fullNameController.text != state.fullName) {
      _fullNameController.text = state.fullName;
    }
    if (_emailController.text != state.email) {
      _emailController.text = state.email;
    }
    if (_passwordController.text != state.password) {
      _passwordController.text = state.password;
    }
    if (_phoneController.text != state.phone) {
      _phoneController.text = state.phone;
    }
    if (state.dateOfBirth != null && 
        _dateOfBirthController.text != DateFormat('yyyy-MM-dd').format(state.dateOfBirth!)) {
      _dateOfBirthController.text = DateFormat('yyyy-MM-dd').format(state.dateOfBirth!);
    }

    // Trigger registration
    notifier.register();
  }
}