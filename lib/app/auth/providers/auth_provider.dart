import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:techrar_gym/app/auth/services/auth_service.dart';

import '../../../core/api/api_response.dart';
import '../../../core/api/api_service.dart';

part "auth_provider.g.dart";

@Riverpod(keepAlive: true)
class AuthNotifier extends _$AuthNotifier {
  AuthService get _service => ref.read(authServiceProvider);
  @override
  FutureOr<bool> build() async {
    /// Check if the user is already logged in when the app starts
    return await _service.isLoggedIn();
  }

  Future<void> login(String email, String password) async {
    final AsyncValue<bool> current = state;
    state = AsyncLoading()..copyWithPrevious(current);

    // For now, just simulate a successful login
    // TODO: Implement actual login with server
    // await Future.delayed(Duration(seconds: 2));
    // state = AsyncData(true);

    // Uncomment when ready to use actual service
    try {
      final result = await _service.login(
        email: email,
        password: password,
      );

      result.fold(
        (error) {
          state = AsyncError(Exception(error), StackTrace.current);
        },
        (response) {
          state = AsyncData(true);
        },
      );
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  Future<void> logout() async {
    final AsyncValue<bool> current = state;
    state = AsyncLoading()..copyWithPrevious(current);

    try {
      await _service.logout();
      state = AsyncData(false);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  FutureEither<ApiResponse<Map<String, dynamic>>> register({
    required String fullName,
    required String email,
    required String password,
    required String phone,
    required String gender,
    required DateTime dateOfBirth,
  }) async {
    return await _service.register(
      fullName: fullName,
      email: email,
      password: password,
      phone: phone,
      gender: gender,
      dateOfBirth: dateOfBirth,
    );
  }
}
