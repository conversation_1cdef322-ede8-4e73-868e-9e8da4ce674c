import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:techrar_gym/app/auth/models/signup_view_state.dart';
import 'package:techrar_gym/app/auth/providers/auth_provider.dart';
import 'package:techrar_gym/core/api/api_exceptions.dart';

part 'signup_provider.g.dart';

@riverpod
class SignupNotifier extends _$SignupNotifier {
  @override
  SignupViewState build() {
    return SignupViewState();
  }

  void updateFullName(String value) {
    state = state.copyWith(fullName: value, fullNameError: null);
  }

  void updateEmail(String value) {
    state = state.copyWith(email: value, emailError: null);
  }

  void updatePassword(String value) {
    state = state.copyWith(password: value, passwordError: null);
  }

  void updatePhone(String value) {
    state = state.copyWith(phone: value, phoneError: null);
  }

  void updateGender(String value) {
    state = state.copyWith(gender: value);
  }

  void updateDateOfBirth(DateTime value) {
    state = state.copyWith(dateOfBirth: value, dateOfBirthError: null);
  }

  void togglePasswordVisibility() {
    state = state.copyWith(obscurePassword: !state.obscurePassword);
  }

  String? _validateFullName(String fullName) {
    if (fullName.isEmpty) {
      return 'Full name is required';
    }
    if (fullName.length < 2) {
      return 'Full name must be at least 2 characters';
    }
    return null;
  }

  String? _validateEmail(String email) {
    if (email.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String password) {
    if (password.isEmpty) {
      return 'Password is required';
    }
    if (password.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? _validatePhone(String phone) {
    if (phone.isEmpty) {
      return 'Phone number is required';
    }
    if (!RegExp(r'^(\+966|966|0)?(5[0-9]|1[1-9]|2[1-9]|3[1-9]|4[1-9]|7[1-9])[0-9]{7}$').hasMatch(phone)) {
      return 'Please enter a valid Saudi phone number';
    }
    return null;
  }

  String? _validateDateOfBirth(DateTime? dateOfBirth) {
    if (dateOfBirth == null) {
      return 'Date of birth is required';
    }
    return null;
  }

  Future<void> register() async {
    // Validate all fields
    final fullNameError = _validateFullName(state.fullName);
    final emailError = _validateEmail(state.email);
    final passwordError = _validatePassword(state.password);
    final phoneError = _validatePhone(state.phone);
    final dateOfBirthError = _validateDateOfBirth(state.dateOfBirth);

    if (fullNameError != null ||
        emailError != null ||
        passwordError != null ||
        phoneError != null ||
        dateOfBirthError != null) {
      state = state.copyWith(
        fullNameError: fullNameError,
        emailError: emailError,
        passwordError: passwordError,
        phoneError: phoneError,
        dateOfBirthError: dateOfBirthError,
      );
      return;
    }

    state = state.copyWith(isLoading: true, generalError: null);

    try {
      final result = await ref.read(authNotifierProvider.notifier).register(
            fullName: state.fullName,
            email: state.email,
            password: state.password,
            phone: state.phone,
            gender: state.gender,
            dateOfBirth: state.dateOfBirth!,
          );

      result.fold(
        (error) {
          state = state.copyWith(
            isLoading: false,
            generalError: error,
          );
        },
        (response) {
          state = state.copyWith(
            isLoading: false,
            isSuccess: true,
          );
        },
      );
    } on ValidationException catch (e) {
      String? fullNameError;
      String? emailError;
      String? passwordError;
      String? phoneError;
      String? dateOfBirthError;
      String? generalError;

      for (final error in e.errors) {
        switch (error.field.toLowerCase()) {
          case 'full_name':
            fullNameError = error.message;
            break;
          case 'email':
            emailError = error.message;
            break;
          case 'password':
            passwordError = error.message;
            break;
          case 'phone':
            phoneError = error.message;
            break;
          case 'date_of_birth':
            dateOfBirthError = error.message;
            break;
          default:
            generalError = error.message;
        }
      }

      state = state.copyWith(
        isLoading: false,
        fullNameError: fullNameError,
        emailError: emailError,
        passwordError: passwordError,
        phoneError: phoneError,
        dateOfBirthError: dateOfBirthError,
        generalError: generalError ?? e.message,
      );
    } on ApiException catch (e) {
      state = state.copyWith(
        isLoading: false,
        generalError: e.message,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        generalError: 'An unexpected error occurred. Please try again.',
      );
    }
  }
}
