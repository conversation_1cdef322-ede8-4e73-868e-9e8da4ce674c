import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:techrar_gym/app/auth/models/login_view_state.dart';
import 'package:techrar_gym/app/auth/providers/auth_provider.dart';
import 'package:techrar_gym/core/api/api_exceptions.dart';
part 'login_provider.g.dart';

@riverpod
class LoginNotifier extends _$LoginNotifier {
  @override
  LoginViewState build() {
    return LoginViewState(
      email: '',
      password: '',
    );
  }

  void updateEmail(String email) {
    state = state.copyWith(
      email: email,
      emailError: _validateEmail(email),
      generalError: null, // Clear general error when user starts typing
    );
  }

  void updatePassword(String password) {
    state = state.copyWith(
      password: password,
      passwordError: _validatePassword(password),
      generalError: null, // Clear general error when user starts typing
    );
  }

  void togglePasswordVisibility() {
    state = state.copyWith(obscurePassword: !state.obscurePassword);
  }

  void clearErrors() {
    state = state.copyWith(
      emailError: null,
      passwordError: null,
      generalError: null,
    );
  }

  String? _validateEmail(String email) {
    if (email.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String password) {
    if (password.isEmpty) {
      return 'Password is required';
    }
    if (password.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  Future<void> login() async {
    // Validate all fields
    final emailError = _validateEmail(state.email);
    final passwordError = _validatePassword(state.password);

    if (emailError != null || passwordError != null) {
      state = state.copyWith(
        emailError: emailError,
        passwordError: passwordError,
      );
      return;
    }

    state = state.copyWith(isLoading: true, generalError: null);

    try {
      await ref.read(authNotifierProvider.notifier).login(
            state.email,
            state.password,
          );

      // Check if login was successful by watching the auth state
      final authState = ref.read(authNotifierProvider);
      authState.when(
        data: (isAuthenticated) {
          if (isAuthenticated) {
            // Login successful - reset loading state
            state = state.copyWith(isLoading: false);
          } else {
            // Login failed but no exception was thrown
            state = state.copyWith(
              isLoading: false,
              generalError: 'Login failed. Please check your credentials.',
            );
          }
        },
        loading: () {
          // Still loading, keep current state
        },
        error: (error, stackTrace) {
          // Handle auth provider errors
          state = state.copyWith(
            isLoading: false,
            generalError: error.toString().contains('Exception:')
                ? error.toString().replaceFirst('Exception: ', '')
                : 'Login failed. Please try again.',
          );
        },
      );
    } on ValidationException catch (e) {
      String? emailError;
      String? passwordError;
      String? generalError;

      for (final error in e.errors) {
        switch (error.field.toLowerCase()) {
          case 'email':
            emailError = error.message;
            break;
          case 'password':
            passwordError = error.message;
            break;
          default:
            generalError = error.message;
        }
      }

      state = state.copyWith(
        isLoading: false,
        emailError: emailError,
        passwordError: passwordError,
        generalError: generalError ?? e.message,
      );
    } on ApiException catch (e) {
      state = state.copyWith(
        isLoading: false,
        generalError: e.message,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        generalError: 'An unexpected error occurred. Please try again.',
      );
    }
  }
}
