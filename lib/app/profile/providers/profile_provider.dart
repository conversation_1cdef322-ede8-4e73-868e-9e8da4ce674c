import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:techrar_gym/app/profile/models/profile_state.dart';
import 'package:techrar_gym/app/profile/services/profile_service.dart';

part 'profile_provider.g.dart';

@riverpod
class ProfileNotifier extends _$ProfileNotifier {
  @override
  ProfileState build() {
    // Initialize with loading state and trigger data loading asynchronously
    Future.microtask(() => loadProfileData());
    return const ProfileState(isLoading: true);
  }

  Future<void> loadProfileData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(profileServiceProvider);

      // Load all data concurrently
      final results = await Future.wait([
        service.getCurrentCustomer(),
        service.getMembershipInfo(),
        service.getProfileStats(),
      ]);

      final customerResult = results[0] as dynamic;
      final membershipResult = results[1] as dynamic;
      final statsResult = results[2] as dynamic;

      // Check for errors
      String? error;
      var customer = state.customer;
      var membershipInfo = state.membershipInfo;
      var stats = state.stats;

      customerResult.fold(
        (err) => error ??= err,
        (data) => customer = data,
      );

      membershipResult.fold(
        (err) => error ??= err,
        (data) => membershipInfo = data,
      );

      statsResult.fold(
        (err) => error ??= err,
        (data) => stats = data,
      );

      state = state.copyWith(
        isLoading: false,
        error: error,
        customer: customer,
        membershipInfo: membershipInfo,
        stats: stats,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load profile data: ${e.toString()}',
      );
    }
  }

  Future<void> updateProfile({
    String? fullName,
    String? phone,
    String? profileImageUrl,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(profileServiceProvider);
      final result = await service.updateProfile(
        fullName: fullName,
        phone: phone,
        profileImageUrl: profileImageUrl,
      );

      result.fold(
        (error) {
          state = state.copyWith(
            isLoading: false,
            error: error,
          );
        },
        (updatedCustomer) {
          state = state.copyWith(
            isLoading: false,
            customer: updatedCustomer,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update profile: ${e.toString()}',
      );
    }
  }

  Future<void> refresh() async {
    await loadProfileData();
  }
}
