import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:techrar_gym/app/auth/models/customer.dart';
import 'package:techrar_gym/app/auth/services/auth_service.dart';
import 'package:techrar_gym/app/profile/models/profile_state.dart';

final profileServiceProvider = Provider<ProfileService>(
  (ref) => ProfileService(ref),
);

class ProfileService {
  final Ref _ref;
  ProfileService(this._ref);

  Future<Either<String, Customer?>> getCurrentCustomer() async {
    try {
      final authService = _ref.read(authServiceProvider);
      final customer = await authService.getCurrentCustomer();
      return right(customer);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, MembershipInfo>> getMembershipInfo() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));
      
      return right(MembershipInfo(
        type: 'Premium',
        status: 'Active',
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        startDate: DateTime.now().subtract(const Duration(days: 335)),
        isActive: true,
        daysRemaining: 30,
      ));
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, ProfileStats>> getProfileStats() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));
      
      return right(const ProfileStats(
        totalWorkouts: 45,
        totalClasses: 23,
        currentStreak: 7,
        longestStreak: 15,
        totalHours: 68.5,
        caloriesBurned: 12450,
      ));
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, Customer>> updateProfile({
    String? fullName,
    String? phone,
    String? profileImageUrl,
  }) async {
    try {
      final authService = _ref.read(authServiceProvider);
      final response = await authService.updateProfile(
        fullName: fullName,
        phone: phone,
        profileImageUrl: profileImageUrl,
      );

      if (response.success && response.data != null) {
        return right(response.data!);
      } else {
        return left(response.message ?? 'Failed to update profile');
      }
    } catch (e) {
      return left(e.toString());
    }
  }

  List<MenuItem> getMenuItems() {
    return [
      const MenuItem(
        title: 'Personal Information',
        icon: 'person_outline',
        route: '/profile/personal-info',
      ),
      const MenuItem(
        title: 'Workout History',
        icon: 'history',
        route: '/profile/workout-history',
      ),
      const MenuItem(
        title: 'Membership Details',
        icon: 'card_membership',
        route: '/profile/membership',
      ),
      const MenuItem(
        title: 'Notifications',
        icon: 'notifications_outlined',
        route: '/profile/notifications',
      ),
      const MenuItem(
        title: 'Help & Support',
        icon: 'help_outline',
        route: '/profile/support',
      ),
      const MenuItem(
        title: 'Logout',
        icon: 'logout',
        isDestructive: true,
      ),
    ];
  }
}
