import 'package:techrar_gym/app/auth/models/customer.dart';

class ProfileState {
  final bool isLoading;
  final String? error;
  final Customer? customer;
  final MembershipInfo? membershipInfo;
  final ProfileStats? stats;

  const ProfileState({
    this.isLoading = false,
    this.error,
    this.customer,
    this.membershipInfo,
    this.stats,
  });

  ProfileState copyWith({
    bool? isLoading,
    String? error,
    Customer? customer,
    MembershipInfo? membershipInfo,
    ProfileStats? stats,
  }) {
    return ProfileState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      customer: customer ?? this.customer,
      membershipInfo: membershipInfo ?? this.membershipInfo,
      stats: stats ?? this.stats,
    );
  }
}

class MembershipInfo {
  final String type;
  final String status;
  final DateTime? expiryDate;
  final DateTime? startDate;
  final bool isActive;
  final int daysRemaining;

  const MembershipInfo({
    required this.type,
    required this.status,
    this.expiryDate,
    this.startDate,
    required this.isActive,
    required this.daysRemaining,
  });
}

class ProfileStats {
  final int totalWorkouts;
  final int totalClasses;
  final int currentStreak;
  final int longestStreak;
  final double totalHours;
  final int caloriesBurned;

  const ProfileStats({
    required this.totalWorkouts,
    required this.totalClasses,
    required this.currentStreak,
    required this.longestStreak,
    required this.totalHours,
    required this.caloriesBurned,
  });
}

class MenuItem {
  final String title;
  final String icon;
  final String? route;
  final void Function()? onTap;
  final bool isDestructive;

  const MenuItem({
    required this.title,
    required this.icon,
    this.route,
    this.onTap,
    this.isDestructive = false,
  });
}
