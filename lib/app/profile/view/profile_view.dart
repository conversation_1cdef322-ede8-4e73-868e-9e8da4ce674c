import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/app/auth/widgets/logout_confirmation_dialog.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/profile/providers/profile_provider.dart';
import 'package:techrar_gym/app/profile/models/profile_state.dart';

class ProfileView extends ConsumerWidget {
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileState = ref.watch(profileNotifierProvider);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.backgroundColor,
        elevation: AppSizes.appBarElevation,
        title: Text(
          'Profile',
          style: TextStyles.h2.copyWith(
            color: AppColors.primaryTextColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement settings
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Settings feature coming soon!'),
                  backgroundColor: AppColors.infoColor,
                ),
              );
            },
            icon: Icon(
              Icons.settings_rounded,
              color: AppColors.primaryTextColor,
              size: AppSizes.iconMd,
            ),
          ),
          const SizedBox(width: Insets.m),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(profileNotifierProvider.notifier).refresh(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(Insets.l),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Header
              _buildProfileHeader(profileState),
              const SizedBox(height: Insets.xl),

              // Membership Status
              _buildMembershipStatus(profileState),
              const SizedBox(height: Insets.xl),

              // Profile Stats
              _buildProfileStats(profileState),
              const SizedBox(height: Insets.xl),

              // Menu Options
              _buildProfileMenu(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader(ProfileState profileState) {
    return Container(
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
      ),
      child: profileState.isLoading ? _buildProfileHeaderLoading() : _buildProfileHeaderContent(profileState),
    );
  }

  Widget _buildProfileHeaderLoading() {
    return Row(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.inputBorderColor,
            borderRadius: BorderRadius.circular(40),
          ),
        ),
        const SizedBox(width: Insets.l),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                height: 20,
                decoration: BoxDecoration(
                  color: AppColors.inputBorderColor,
                  borderRadius: BorderRadius.circular(AppSizes.radiusSm),
                ),
              ),
              const SizedBox(height: Insets.s),
              Container(
                width: 150,
                height: 16,
                decoration: BoxDecoration(
                  color: AppColors.inputBorderColor,
                  borderRadius: BorderRadius.circular(AppSizes.radiusSm),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfileHeaderContent(ProfileState profileState) {
    return Row(
      children: [
        CircleAvatar(
          radius: 40,
          backgroundColor: AppColors.primaryColor,
          backgroundImage: profileState.customer?.profileImageUrl != null
              ? NetworkImage(profileState.customer!.profileImageUrl!)
              : null,
          child: profileState.customer?.profileImageUrl == null
              ? Icon(
                  Icons.person_rounded,
                  color: AppColors.white,
                  size: AppSizes.iconXl,
                )
              : null,
        ),
        const SizedBox(width: Insets.l),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                profileState.customer?.fullName ?? 'John Doe',
                style: TextStyles.h1b.copyWith(
                  color: AppColors.primaryTextColor,
                ),
              ),
              const SizedBox(height: Insets.xs),
              Text(
                profileState.customer?.email ?? '<EMAIL>',
                style: TextStyles.body2.copyWith(
                  color: AppColors.secondaryTextColor,
                ),
              ),
              const SizedBox(height: Insets.s),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: Insets.m,
                  vertical: Insets.xs,
                ),
                decoration: BoxDecoration(
                  color: AppColors.successColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSizes.buttonRadiusSm),
                ),
                child: Text(
                  'Premium Member',
                  style: TextStyles.caption.copyWith(
                    color: AppColors.successColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        Builder(
          builder: (context) => IconButton(
            onPressed: () {
              // TODO: Implement edit profile
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Edit profile feature coming soon!'),
                  backgroundColor: AppColors.infoColor,
                ),
              );
            },
            icon: Icon(
              Icons.edit_rounded,
              color: AppColors.primaryColor,
              size: AppSizes.iconMd,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMembershipStatus(ProfileState profileState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Membership Status',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        Container(
          padding: const EdgeInsets.all(Insets.l),
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow,
          ),
          child:
              profileState.isLoading ? _buildMembershipLoading() : _buildMembershipContent(profileState.membershipInfo),
        ),
      ],
    );
  }

  Widget _buildMembershipLoading() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          height: 20,
          decoration: BoxDecoration(
            color: AppColors.inputBorderColor,
            borderRadius: BorderRadius.circular(AppSizes.radiusSm),
          ),
        ),
        const SizedBox(height: Insets.m),
        Container(
          width: double.infinity,
          height: 16,
          decoration: BoxDecoration(
            color: AppColors.inputBorderColor,
            borderRadius: BorderRadius.circular(AppSizes.radiusSm),
          ),
        ),
      ],
    );
  }

  Widget _buildMembershipContent(dynamic membershipInfo) {
    if (membershipInfo == null) {
      return Center(
        child: Text(
          'No membership information available',
          style: TextStyles.body2.copyWith(
            color: AppColors.secondaryTextColor,
          ),
        ),
      );
    }

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  membershipInfo.type,
                  style: TextStyles.h2b.copyWith(
                    color: AppColors.primaryTextColor,
                  ),
                ),
                const SizedBox(height: Insets.xs),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: Insets.m,
                    vertical: Insets.xs,
                  ),
                  decoration: BoxDecoration(
                    color: membershipInfo.isActive
                        ? AppColors.successColor.withValues(alpha: 0.1)
                        : AppColors.errorColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppSizes.buttonRadiusSm),
                  ),
                  child: Text(
                    membershipInfo.status,
                    style: TextStyles.caption.copyWith(
                      color: membershipInfo.isActive ? AppColors.successColor : AppColors.errorColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${membershipInfo.daysRemaining} days',
                  style: TextStyles.h2b.copyWith(
                    color: AppColors.primaryColor,
                  ),
                ),
                const SizedBox(height: Insets.xs),
                Text(
                  'remaining',
                  style: TextStyles.caption.copyWith(
                    color: AppColors.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: Insets.l),
        if (membershipInfo.expiryDate != null) ...[
          Row(
            children: [
              Icon(
                Icons.event_outlined,
                color: AppColors.secondaryTextColor,
                size: AppSizes.iconSm,
              ),
              const SizedBox(width: Insets.s),
              Text(
                'Expires on ${_formatDate(membershipInfo.expiryDate!)}',
                style: TextStyles.body2.copyWith(
                  color: AppColors.secondaryTextColor,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildProfileStats(ProfileState profileState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Stats',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        Container(
          padding: const EdgeInsets.all(Insets.l),
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow,
          ),
          child: profileState.isLoading ? _buildStatsLoading() : _buildStatsContent(profileState.stats),
        ),
      ],
    );
  }

  Widget _buildStatsLoading() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 2,
      crossAxisSpacing: Insets.m,
      mainAxisSpacing: Insets.m,
      children: List.generate(4, (index) {
        return Container(
          decoration: BoxDecoration(
            color: AppColors.inputBorderColor,
            borderRadius: BorderRadius.circular(AppSizes.radiusSm),
          ),
        );
      }),
    );
  }

  Widget _buildStatsContent(dynamic stats) {
    if (stats == null) {
      return Center(
        child: Text(
          'No stats available',
          style: TextStyles.body2.copyWith(
            color: AppColors.secondaryTextColor,
          ),
        ),
      );
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 2,
      crossAxisSpacing: Insets.m,
      mainAxisSpacing: Insets.m,
      children: [
        _buildStatCard(
          'Workouts',
          stats.totalWorkouts.toString(),
          Icons.fitness_center_rounded,
          AppColors.primaryColor,
        ),
        _buildStatCard(
          'Classes',
          stats.totalClasses.toString(),
          Icons.group_rounded,
          AppColors.successColor,
        ),
        _buildStatCard(
          'Streak',
          '${stats.currentStreak} days',
          Icons.local_fire_department_rounded,
          AppColors.warningColor,
        ),
        _buildStatCard(
          'Hours',
          '${stats.totalHours.toStringAsFixed(1)}h',
          Icons.timer_rounded,
          AppColors.infoColor,
        ),
      ],
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(Insets.m),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusMd),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: AppSizes.iconMd,
          ),
          const SizedBox(height: Insets.xs),
          Text(
            value,
            style: TextStyles.h2b.copyWith(
              color: AppColors.primaryTextColor,
            ),
          ),
          Text(
            label,
            style: TextStyles.caption.copyWith(
              color: AppColors.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileMenu(BuildContext context) {
    final menuItems = [
      {'title': 'Personal Information', 'icon': Icons.person_outline_rounded, 'isDestructive': false},
      {'title': 'Workout History', 'icon': Icons.history_rounded, 'isDestructive': false},
      {'title': 'Membership Details', 'icon': Icons.card_membership_rounded, 'isDestructive': false},
      {'title': 'Notifications', 'icon': Icons.notifications_outlined, 'isDestructive': false},
      {'title': 'Help & Support', 'icon': Icons.help_outline_rounded, 'isDestructive': false},
      {'title': 'Logout', 'icon': Icons.logout_rounded, 'isDestructive': true},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Menu',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        Container(
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow,
          ),
          child: Column(
            children: menuItems.map((item) {
              final isLast = item == menuItems.last;
              return Column(
                children: [
                  _buildMenuItem(context, item),
                  if (!isLast) _buildDivider(),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem(BuildContext context, Map<String, dynamic> item) {
    return InkWell(
      onTap: () => _handleMenuItemTap(context, item['title']),
      borderRadius: BorderRadius.circular(AppSizes.cardRadius),
      child: Padding(
        padding: const EdgeInsets.all(Insets.l),
        child: Row(
          children: [
            Icon(
              item['icon'],
              color: item['isDestructive'] ? AppColors.errorColor : AppColors.primaryColor,
              size: AppSizes.iconMd,
            ),
            const SizedBox(width: Insets.l),
            Expanded(
              child: Text(
                item['title'],
                style: TextStyles.body1.copyWith(
                  color: item['isDestructive'] ? AppColors.errorColor : AppColors.primaryTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              color: AppColors.secondaryTextColor,
              size: AppSizes.iconSm,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: AppSizes.dividerThickness,
      color: AppColors.inputBorderColor,
      indent: Insets.l + AppSizes.iconMd + Insets.l,
    );
  }

  Future<void> _handleMenuItemTap(BuildContext context, String menuItem) async {
    if (menuItem == 'Logout') {
      final shouldLogout = await showLogoutConfirmationDialog(context);
      if (shouldLogout == true) {
        // Logout was successful, navigation will be handled by AuthGard
      }
    } else {
      // TODO: Implement other menu item actions
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$menuItem feature coming soon!'),
          backgroundColor: AppColors.infoColor,
        ),
      );
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
