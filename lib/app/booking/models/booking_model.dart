import 'class_session_model.dart';
import 'gym_class_model.dart';

class Booking {
  final String id;
  final String merchantId;
  final String memberId;
  final String sessionId;
  final String status;
  final String? bookingNotes;
  final DateTime bookingDate;
  final ClassSession? session;
  final GymClass? gymClass;

  Booking({
    required this.id,
    required this.merchantId,
    required this.memberId,
    required this.sessionId,
    required this.status,
    this.bookingNotes,
    required this.bookingDate,
    this.session,
    this.gymClass,
  });

  factory Booking.fromJson(Map<String, dynamic> json) {
    return Booking(
      id: json['id'],
      merchantId: json['merchant_id'],
      memberId: json['member_id'],
      sessionId: json['session_id'],
      status: json['status'],
      bookingNotes: json['booking_notes'],
      bookingDate: DateTime.parse(json['booking_date']),
      session: json['session'] != null ? ClassSession.fromJson(json['session']) : null,
      gymClass: json['session']?['class'] != null ? GymClass.fromJson(json['session']['class']) : null,
    );
  }

  bool get isConfirmed => status == 'confirmed';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
}
