class Trainer {
  final String id;
  final String fullName;
  final String? profileImageUrl;
  final List<String>? specializations;

  Trainer({
    required this.id,
    required this.fullName,
    this.profileImageUrl,
    this.specializations,
  });

  factory Trainer.fromJson(Map<String, dynamic> json) {
    return Trainer(
      id: json['id'],
      fullName: json['full_name'],
      profileImageUrl: json['profile_image_url'],
      specializations: json['specializations'] != null
          ? List<String>.from(json['specializations'])
          : null,
    );
  }
}