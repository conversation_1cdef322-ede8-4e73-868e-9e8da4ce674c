class ClassSession {
  final String id;
  final DateTime startTime;
  final DateTime endTime;
  final String status;
  final int bookedCount;
  final int availableSpots;
  final bool isFull;
  final bool isCancelled;

  ClassSession({
    required this.id,
    required this.startTime,
    required this.endTime,
    required this.status,
    required this.bookedCount,
    required this.availableSpots,
    required this.isFull,
    required this.isCancelled,
  });

  factory ClassSession.fromJson(Map<String, dynamic> json) {
    return ClassSession(
      id: json['id'],
      startTime: DateTime.parse(json['start_time']),
      endTime: DateTime.parse(json['end_time']),
      status: json['status'],
      bookedCount: json['booked_count'],
      availableSpots: json['available_spots'],
      isFull: json['is_full'],
      isCancelled: json['is_cancelled'],
    );
  }
}