import '../../shared/models/branch.dart';
import 'class_session_model.dart';
import 'trainer_model.dart';

class GymClass {
  final String id;
  final String name;
  final String description;
  final int capacity;
  final int duration;
  final double price;
  final String difficulty;
  final bool isPersonalTraining;
  final String room;
  final Trainer trainer;
  final Branch branch;
  final List<ClassSession> sessions;

  GymClass({
    required this.id,
    required this.name,
    required this.description,
    required this.capacity,
    required this.duration,
    required this.price,
    required this.difficulty,
    required this.isPersonalTraining,
    required this.room,
    required this.trainer,
    required this.branch,
    required this.sessions,
  });

  factory GymClass.fromJson(Map<String, dynamic> json) {
    return GymClass(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      capacity: json['capacity'],
      duration: json['duration'],
      price: json['price'].toDouble(),
      difficulty: json['difficulty'],
      isPersonalTraining: json['is_personal_training'],
      room: json['room'],
      trainer: Trainer.from<PERSON><PERSON>(json['trainer']),
      branch: Branch.fromJson(json['branch']),
      sessions: (json['sessions'] as List).map((s) => ClassSession.fromJson(s)).toList(),
    );
  }
}
