import '../models/gym_class_model.dart';
import '../models/booking_model.dart';
import '../../../core/api/api_response.dart';
import '../../../core/api/api_constants.dart';
import '../../../core/api/api_service.dart';

class BookingService {
  static Future<ApiResponse<List<GymClass>>> getClasses({
    DateTime? from,
    DateTime? to,
    String? difficulty,
    String? trainerId,
  }) async {
    final queryParams = <String, String>{};
    if (from != null) queryParams['from'] = from.toIso8601String();
    if (to != null) queryParams['to'] = to.toIso8601String();
    if (difficulty != null) queryParams['difficulty'] = difficulty;
    if (trainerId != null) queryParams['trainer_id'] = trainerId;

    return await ApiService.get<List<GymClass>>(
      ApiConstants.classes,
      queryParams: queryParams,
      includeAuth: false,
      fromJsonT: (data) => (data['data'] as List).map((gymClass) => GymClass.fromJson(gymClass)).toList(),
    );
  }

  static Future<ApiResponse<Map<String, dynamic>>> createBooking({
    required String memberId,
    required String sessionId,
    String? bookingNotes,
  }) async {
    final body = {
      'member_id': memberId,
      'session_id': sessionId,
    };

    if (bookingNotes != null) body['booking_notes'] = bookingNotes;

    return await ApiService.post<Map<String, dynamic>>(
      ApiConstants.bookings,
      body: body,
      includeAuth: false,
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }

  static Future<ApiResponse<List<Booking>>> getMemberBookings({
    required String memberId,
    String? timeframe, // 'upcoming', 'past'
    String? status, // 'confirmed', 'completed', 'cancelled'
  }) async {
    final queryParams = <String, String>{
      'member_id': memberId,
    };

    if (timeframe != null) queryParams['timeframe'] = timeframe;
    if (status != null) queryParams['status'] = status;

    return await ApiService.get<List<Booking>>(
      ApiConstants.bookings,
      queryParams: queryParams,
      includeAuth: false,
      fromJsonT: (data) => (data['data'] as List).map((booking) => Booking.fromJson(booking)).toList(),
    );
  }

  static Future<ApiResponse<Map<String, dynamic>>> cancelBooking({
    required String bookingId,
    String? reason,
  }) async {
    final body = <String, dynamic>{};
    if (reason != null) body['reason'] = reason;

    return await ApiService.delete<Map<String, dynamic>>(
      '${ApiConstants.bookings}/$bookingId',
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }
}
