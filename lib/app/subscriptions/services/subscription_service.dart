// lib/data/services/subscription_service.dart
import 'package:techrar_gym/app/subscriptions/models/subscription.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan.dart';

import '../../../core/api/api_response.dart';
import '../../../core/api/api_constants.dart';
import '../../../core/api/api_service.dart';

class SubscriptionService {
  static Future<ApiResponse<List<SubscriptionPlan>>> getSubscriptionPlans({
    bool? isActive,
  }) async {
    final queryParams = <String, String>{};
    if (isActive != null) queryParams['is_active'] = isActive.toString();

    return await ApiService.get<List<SubscriptionPlan>>(
      ApiConstants.subscriptionPlans,
      queryParams: queryParams,
      includeAuth: false,
      fromJsonT: (data) => (data['data'] as List).map((plan) => SubscriptionPlan.fromJson(plan)).toList(),
    );
  }

  static Future<ApiResponse<Map<String, dynamic>>> purchaseSubscription({
    required String planId,
    required String paymentMethod,
    bool autoRenew = true,
    String? promoCode,
  }) async {
    final body = {
      'plan_id': planId,
      'payment_method': paymentMethod,
      'auto_renew': autoRenew,
    };

    if (promoCode != null) body['promo_code'] = promoCode;

    return await ApiService.post<Map<String, dynamic>>(
      ApiConstants.subscriptions,
      body: body,
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }

  static Future<ApiResponse<List<Subscription>>> getCustomerSubscriptions({
    bool includeHistory = false,
  }) async {
    final queryParams = <String, String>{};
    if (includeHistory) queryParams['include_history'] = 'true';

    return await ApiService.get<List<Subscription>>(
      ApiConstants.subscriptions,
      queryParams: queryParams,
      fromJsonT: (data) => (data['data'] as List).map((subscription) => Subscription.fromJson(subscription)).toList(),
    );
  }

  static Future<ApiResponse<Map<String, dynamic>>> freezeSubscription({
    required String reason,
    required int days,
    String? note,
  }) async {
    final body = {
      'reason': reason,
      'days': days,
    };

    if (note != null) body['note'] = note;

    return await ApiService.post<Map<String, dynamic>>(
      ApiConstants.freezeSubscription,
      body: body,
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }

  static Future<ApiResponse<Map<String, dynamic>>> unfreezeSubscription() async {
    return await ApiService.post<Map<String, dynamic>>(
      '${ApiConstants.subscriptions}/unfreeze',
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }

  static Future<ApiResponse<Map<String, dynamic>>> cancelSubscription({
    required String reason,
    String? feedback,
    bool immediate = false,
  }) async {
    final body = {
      'reason': reason,
      'immediate': immediate,
    };

    if (feedback != null) body['feedback'] = feedback;

    return await ApiService.post<Map<String, dynamic>>(
      ApiConstants.cancelSubscription,
      body: body,
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }
}
