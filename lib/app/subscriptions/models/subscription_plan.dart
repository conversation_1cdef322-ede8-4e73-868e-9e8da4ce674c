import '../../shared/models/branch.dart';

class SubscriptionPlan {
  final String id;
  final String name;
  final String description;
  final double price;
  final String currency;
  final int duration;
  final String durationType;
  final int classesPerWeek;
  final int freezeCredits;
  final List<String> features;
  final bool isPopular;
  final Branch? branch;

  SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.duration,
    required this.durationType,
    required this.classesPerWeek,
    required this.freezeCredits,
    required this.features,
    required this.isPopular,
    this.branch,
  });

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlan(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      price: json['price'].toDouble(),
      currency: json['currency'],
      duration: json['duration'],
      durationType: json['duration_type'],
      classesPerWeek: json['classes_per_week'],
      freezeCredits: json['freeze_credits'],
      features: List<String>.from(json['features']),
      isPopular: json['is_popular'] ?? false,
      branch: json['branch'] != null ? Branch.fromJson(json['branch']) : null,
    );
  }
}
