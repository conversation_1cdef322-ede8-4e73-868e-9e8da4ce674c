import 'subscription_plan.dart';

class Subscription {
  final String id;
  final String status;
  final DateTime startDate;
  final DateTime endDate;
  final int daysRemaining;
  final int freezeDaysUsed;
  final int freezeAttemptsUsed;
  final int classesUsedThisWeek;
  final SubscriptionPlan subscriptionPlan;

  Subscription({
    required this.id,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.daysRemaining,
    required this.freezeDaysUsed,
    required this.freezeAttemptsUsed,
    required this.classesUsedThisWeek,
    required this.subscriptionPlan,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'],
      status: json['status'],
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      daysRemaining: json['days_remaining'],
      freezeDaysUsed: json['freeze_days_used'],
      freezeAttemptsUsed: json['freeze_attempts_used'],
      classesUsedThisWeek: json['classes_used_this_week'],
      subscriptionPlan: SubscriptionPlan.fromJson(json['subscription_plans']),
    );
  }

  bool get isActive => status == 'active';
  bool get isFrozen => status == 'frozen';
  bool get isCancelled => status == 'cancelled';
  bool get isExpired => DateTime.now().isAfter(endDate);
}
