import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/core/services/storage_service.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/home/<USER>/home_provider.dart';
import 'package:techrar_gym/app/home/<USER>/welcome_section.dart';
import 'package:techrar_gym/app/home/<USER>/quick_actions_section.dart';
import 'package:techrar_gym/app/home/<USER>/recent_activity_section.dart';
import 'package:techrar_gym/app/home/<USER>/upcoming_classes_section.dart';

class HomeTabView extends ConsumerWidget {
  const HomeTabView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeState = ref.watch(homeNotifierProvider);

    // Debug: Log access token
    StorageService.getAccessToken().then((token) {
      if (token != null) {
        log('Access token: $token');
      }
    });

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.backgroundColor,
        elevation: AppSizes.appBarElevation,
        title: Text(
          'Techrar Gym',
          style: TextStyles.h2.copyWith(
            color: AppColors.primaryTextColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement notifications
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Notifications feature coming soon!'),
                  backgroundColor: AppColors.infoColor,
                ),
              );
            },
            icon: Icon(
              Icons.notifications_outlined,
              color: AppColors.primaryTextColor,
              size: AppSizes.iconMd,
            ),
          ),
          const SizedBox(width: Insets.m),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(homeNotifierProvider.notifier).refresh(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(Insets.l),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              const WelcomeSection(),
              const SizedBox(height: Insets.xl),

              // Quick Actions
              QuickActionsSection(
                quickActions: homeState.quickActions,
                isLoading: homeState.isLoading,
              ),
              const SizedBox(height: Insets.xl),

              // Recent Activity
              RecentActivitySection(
                recentActivities: homeState.recentActivities,
                isLoading: homeState.isLoading,
              ),
              const SizedBox(height: Insets.xl),

              // Upcoming Classes
              UpcomingClassesSection(
                upcomingClasses: homeState.upcomingClasses,
                isLoading: homeState.isLoading,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
