import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:techrar_gym/app/home/<USER>/home_state.dart';
import 'package:techrar_gym/app/home/<USER>/home_service.dart';

part 'home_provider.g.dart';

@riverpod
class HomeNotifier extends _$HomeNotifier {
  @override
  HomeState build() {
    // Initialize with loading state and trigger data loading
    Future.microtask(() => loadHomeData());
    return const HomeState(isLoading: true);
  }

  Future<void> loadHomeData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(homeServiceProvider);

      // Load all data concurrently
      final results = await Future.wait([
        service.getQuickActions(),
        service.getRecentActivities(),
        service.getUpcomingClasses(),
        service.getUserStats(),
      ]);

      final quickActionsResult = results[0] as dynamic;
      final recentActivitiesResult = results[1] as dynamic;
      final upcomingClassesResult = results[2] as dynamic;
      final userStatsResult = results[3] as dynamic;

      // Check for errors
      String? error;
      List<QuickAction> quickActions = [];
      List<RecentActivity> recentActivities = [];
      List<UpcomingClass> upcomingClasses = [];
      UserStats? userStats;

      quickActionsResult.fold(
        (err) => error ??= err,
        (data) => quickActions = data,
      );

      recentActivitiesResult.fold(
        (err) => error ??= err,
        (data) => recentActivities = data,
      );

      upcomingClassesResult.fold(
        (err) => error ??= err,
        (data) => upcomingClasses = data,
      );

      userStatsResult.fold(
        (err) => error ??= err,
        (data) => userStats = data,
      );

      state = state.copyWith(
        isLoading: false,
        error: error,
        quickActions: quickActions,
        recentActivities: recentActivities,
        upcomingClasses: upcomingClasses,
        userStats: userStats,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load home data: ${e.toString()}',
      );
    }
  }

  Future<void> refresh() async {
    await loadHomeData();
  }
}
