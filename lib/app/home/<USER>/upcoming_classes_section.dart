import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/home/<USER>/home_state.dart';

class UpcomingClassesSection extends ConsumerWidget {
  final List<UpcomingClass> upcomingClasses;
  final bool isLoading;

  const UpcomingClassesSection({
    super.key,
    required this.upcomingClasses,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Upcoming Classes',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        Container(
          padding: const EdgeInsets.all(Insets.l),
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow,
          ),
          child: isLoading
              ? _buildLoadingState()
              : upcomingClasses.isEmpty
                  ? _buildEmptyState()
                  : _buildClassesList(),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(Insets.l),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.event_outlined,
            size: AppSizes.iconXl,
            color: AppColors.secondaryTextColor,
          ),
          const SizedBox(height: Insets.s),
          Text(
            'No upcoming classes',
            style: TextStyles.body2.copyWith(
              color: AppColors.secondaryTextColor,
            ),
          ),
          const SizedBox(height: Insets.xs),
          Text(
            'Book a class to see it here',
            style: TextStyles.caption.copyWith(
              color: AppColors.hintTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildClassesList() {
    return Column(
      children: upcomingClasses.map((upcomingClass) {
        return Padding(
          padding: const EdgeInsets.only(bottom: Insets.m),
          child: _buildClassItem(upcomingClass),
        );
      }).toList(),
    );
  }

  Widget _buildClassItem(UpcomingClass upcomingClass) {
    final availableSpots = upcomingClass.capacity - upcomingClass.enrolled;
    final isAlmostFull = availableSpots <= 3;

    return Container(
      padding: const EdgeInsets.all(Insets.m),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(AppSizes.radiusMd),
        border: Border.all(
          color: AppColors.inputBorderColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  upcomingClass.name,
                  style: TextStyles.body1.copyWith(
                    color: AppColors.primaryTextColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: Insets.s,
                  vertical: Insets.xs,
                ),
                decoration: BoxDecoration(
                  color: isAlmostFull 
                      ? AppColors.warningColor.withValues(alpha: 0.1)
                      : AppColors.successColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSizes.buttonRadiusSm),
                ),
                child: Text(
                  '$availableSpots spots left',
                  style: TextStyles.caption.copyWith(
                    color: isAlmostFull ? AppColors.warningColor : AppColors.successColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: Insets.s),
          Row(
            children: [
              Icon(
                Icons.person_outline_rounded,
                size: AppSizes.iconSm,
                color: AppColors.secondaryTextColor,
              ),
              const SizedBox(width: Insets.xs),
              Text(
                upcomingClass.instructor,
                style: TextStyles.body2.copyWith(
                  color: AppColors.secondaryTextColor,
                ),
              ),
              const SizedBox(width: Insets.m),
              Icon(
                Icons.location_on_outlined,
                size: AppSizes.iconSm,
                color: AppColors.secondaryTextColor,
              ),
              const SizedBox(width: Insets.xs),
              Text(
                upcomingClass.location,
                style: TextStyles.body2.copyWith(
                  color: AppColors.secondaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: Insets.s),
          Row(
            children: [
              Icon(
                Icons.access_time_rounded,
                size: AppSizes.iconSm,
                color: AppColors.secondaryTextColor,
              ),
              const SizedBox(width: Insets.xs),
              Text(
                _formatClassTime(upcomingClass.startTime, upcomingClass.endTime),
                style: TextStyles.body2.copyWith(
                  color: AppColors.secondaryTextColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatClassTime(DateTime startTime, DateTime endTime) {
    final now = DateTime.now();
    final isToday = startTime.day == now.day && 
                   startTime.month == now.month && 
                   startTime.year == now.year;
    
    final timeFormat = '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
    final endTimeFormat = '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}';
    
    if (isToday) {
      return 'Today $timeFormat - $endTimeFormat';
    } else {
      final dayFormat = '${startTime.day}/${startTime.month}';
      return '$dayFormat $timeFormat - $endTimeFormat';
    }
  }
}
