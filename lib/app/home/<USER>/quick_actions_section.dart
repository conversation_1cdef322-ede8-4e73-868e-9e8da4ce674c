import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/home/<USER>/home_state.dart';

class QuickActionsSection extends ConsumerWidget {
  final List<QuickAction> quickActions;
  final bool isLoading;

  const QuickActionsSection({
    super.key,
    required this.quickActions,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        if (isLoading)
          _buildLoadingState()
        else if (quickActions.isEmpty)
          _buildEmptyState()
        else
          _buildActionsGrid(),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Row(
      children: [
        Expanded(child: _buildLoadingCard()),
        const SizedBox(width: Insets.m),
        Expanded(child: _buildLoadingCard()),
      ],
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      height: 100,
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
      ),
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
      ),
      child: Center(
        child: Text(
          'No quick actions available',
          style: TextStyles.body2.copyWith(
            color: AppColors.secondaryTextColor,
          ),
        ),
      ),
    );
  }

  Widget _buildActionsGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: Insets.m,
        mainAxisSpacing: Insets.m,
        childAspectRatio: 1.5,
      ),
      itemCount: quickActions.length,
      itemBuilder: (context, index) {
        final action = quickActions[index];
        return _buildActionCard(action);
      },
    );
  }

  Widget _buildActionCard(QuickAction action) {
    return InkWell(
      onTap: () {
        // TODO: Navigate to action route
        // NavigationManager.instance.push(action.route);
      },
      borderRadius: BorderRadius.circular(AppSizes.cardRadius),
      child: Container(
        padding: const EdgeInsets.all(Insets.l),
        decoration: BoxDecoration(
          color: AppColors.surfaceColor,
          borderRadius: BorderRadius.circular(AppSizes.cardRadius),
          boxShadow: Styles.unifiedShadow,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getIconData(action.icon),
              size: AppSizes.iconXl,
              color: AppColors.primaryColor,
            ),
            const SizedBox(height: Insets.s),
            Text(
              action.title,
              style: TextStyles.body2.copyWith(
                color: AppColors.primaryTextColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'fitness_center':
        return Icons.fitness_center_rounded;
      case 'play_arrow':
        return Icons.play_arrow_rounded;
      case 'trending_up':
        return Icons.trending_up_rounded;
      case 'card_membership':
        return Icons.card_membership_rounded;
      default:
        return Icons.help_outline_rounded;
    }
  }
}
