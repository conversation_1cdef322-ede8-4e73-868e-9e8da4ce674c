import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/home/<USER>/home_state.dart';

class RecentActivitySection extends ConsumerWidget {
  final List<RecentActivity> recentActivities;
  final bool isLoading;

  const RecentActivitySection({
    super.key,
    required this.recentActivities,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        Container(
          padding: const EdgeInsets.all(Insets.l),
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow,
          ),
          child: isLoading
              ? _buildLoadingState()
              : recentActivities.isEmpty
                  ? _buildEmptyState()
                  : _buildActivitiesList(),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(Insets.l),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.fitness_center_outlined,
            size: AppSizes.iconXl,
            color: AppColors.secondaryTextColor,
          ),
          const SizedBox(height: Insets.s),
          Text(
            'No recent activity',
            style: TextStyles.body2.copyWith(
              color: AppColors.secondaryTextColor,
            ),
          ),
          const SizedBox(height: Insets.xs),
          Text(
            'Start your first workout to see activity here',
            style: TextStyles.caption.copyWith(
              color: AppColors.hintTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActivitiesList() {
    return Column(
      children: recentActivities.map((activity) {
        return Padding(
          padding: const EdgeInsets.only(bottom: Insets.m),
          child: _buildActivityItem(activity),
        );
      }).toList(),
    );
  }

  Widget _buildActivityItem(RecentActivity activity) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppColors.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppSizes.radiusMd),
          ),
          child: Icon(
            _getActivityIcon(activity.type),
            color: AppColors.primaryColor,
            size: AppSizes.iconMd,
          ),
        ),
        const SizedBox(width: Insets.m),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                activity.title,
                style: TextStyles.body1.copyWith(
                  color: AppColors.primaryTextColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: Insets.xs),
              Text(
                activity.description,
                style: TextStyles.body2.copyWith(
                  color: AppColors.secondaryTextColor,
                ),
              ),
            ],
          ),
        ),
        Text(
          _formatDate(activity.date),
          style: TextStyles.caption.copyWith(
            color: AppColors.hintTextColor,
          ),
        ),
      ],
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'class':
        return Icons.group_rounded;
      case 'workout':
        return Icons.fitness_center_rounded;
      default:
        return Icons.local_activity_rounded;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
