import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:techrar_gym/app/classes/models/class_state.dart';

final classServiceProvider = Provider<ClassService>(
  (ref) => ClassService(ref),
);

class ClassService {
  final Ref _ref;
  ClassService(this._ref);

  Future<Either<String, List<GymClass>>> getClasses({String? category}) async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));
      
      final allClasses = [
        GymClass(
          id: '1',
          name: 'HIIT Training',
          description: 'High-intensity interval training for maximum calorie burn',
          instructor: '<PERSON>',
          category: 'Cardio',
          duration: 45,
          capacity: 20,
          enrolled: 15,
          difficulty: 'Intermediate',
          imageUrl: 'https://example.com/hiit.jpg',
          schedule: [
            DateTime.now().add(const Duration(hours: 2)),
            DateTime.now().add(const Duration(days: 1, hours: 9)),
          ],
          price: 25.0,
          isFeatured: true,
        ),
        GymClass(
          id: '2',
          name: 'Yoga Flow',
          description: 'Gentle yoga flow for flexibility and relaxation',
          instructor: '<PERSON> <PERSON>',
          category: 'Yoga',
          duration: 60,
          capacity: 15,
          enrolled: 8,
          difficulty: 'Beginner',
          imageUrl: 'https://example.com/yoga.jpg',
          schedule: [
            DateTime.now().add(const Duration(hours: 4)),
            DateTime.now().add(const Duration(days: 2, hours: 10)),
          ],
          price: 20.0,
          isFeatured: false,
        ),
        GymClass(
          id: '3',
          name: 'Strength Training',
          description: 'Build muscle and strength with guided weight training',
          instructor: 'Alex Rodriguez',
          category: 'Strength',
          duration: 50,
          capacity: 12,
          enrolled: 10,
          difficulty: 'Advanced',
          imageUrl: 'https://example.com/strength.jpg',
          schedule: [
            DateTime.now().add(const Duration(hours: 6)),
            DateTime.now().add(const Duration(days: 1, hours: 16)),
          ],
          price: 30.0,
          isFeatured: true,
        ),
      ];

      if (category != null && category.isNotEmpty) {
        return right(allClasses.where((c) => c.category == category).toList());
      }

      return right(allClasses);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, List<GymClass>>> getFeaturedClasses() async {
    try {
      final classesResult = await getClasses();
      return classesResult.fold(
        (error) => left(error),
        (classes) => right(classes.where((c) => c.isFeatured).toList()),
      );
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, List<String>>> getCategories() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 300));
      
      return right(['All', 'Cardio', 'Strength', 'Yoga', 'Pilates', 'Dance']);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, ClassBooking>> bookClass({
    required String classId,
    required DateTime scheduledTime,
  }) async {
    try {
      // For now, simulate booking
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(seconds: 1));
      
      final booking = ClassBooking(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        classId: classId,
        customerId: 'current_user_id', // TODO: Get from auth
        scheduledTime: scheduledTime,
        status: 'booked',
        bookingDate: DateTime.now(),
      );

      return right(booking);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, void>> cancelBooking(String bookingId) async {
    try {
      // For now, simulate cancellation
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(seconds: 1));
      
      return right(null);
    } catch (e) {
      return left(e.toString());
    }
  }
}
