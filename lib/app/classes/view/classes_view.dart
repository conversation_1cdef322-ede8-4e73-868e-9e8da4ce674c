import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/classes/providers/class_provider.dart';
import 'package:techrar_gym/app/classes/models/class_state.dart';

class ClassesView extends ConsumerWidget {
  const ClassesView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classState = ref.watch(classNotifierProvider);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.backgroundColor,
        elevation: AppSizes.appBarElevation,
        title: Text(
          'Classes',
          style: TextStyles.h2.copyWith(
            color: AppColors.primaryTextColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement search
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Search feature coming soon!'),
                  backgroundColor: AppColors.infoColor,
                ),
              );
            },
            icon: Icon(
              Icons.search_rounded,
              color: AppColors.primaryTextColor,
              size: AppSizes.iconMd,
            ),
          ),
          const SizedBox(width: Insets.m),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(classNotifierProvider.notifier).refresh(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(Insets.l),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Filter Section
              _buildFilterSection(context, ref, classState),
              const SizedBox(height: Insets.xl),

              // Featured Classes
              _buildFeaturedClasses(classState),
              const SizedBox(height: Insets.xl),

              // All Classes
              _buildAllClasses(context, classState),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Implement book class
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Book class feature coming soon!'),
              backgroundColor: AppColors.infoColor,
            ),
          );
        },
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        child: const Icon(Icons.add_rounded),
      ),
    );
  }

  Widget _buildFilterSection(BuildContext context, WidgetRef ref, ClassState classState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filter Classes',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        if (classState.isLoading) _buildLoadingChips() else _buildFilterChips(ref, classState),
      ],
    );
  }

  Widget _buildLoadingChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: List.generate(5, (index) {
          return Padding(
            padding: EdgeInsets.only(right: index < 4 ? Insets.s : 0),
            child: Container(
              width: 80,
              height: 36,
              decoration: BoxDecoration(
                color: AppColors.inputBorderColor,
                borderRadius: BorderRadius.circular(AppSizes.buttonRadiusSm),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildFilterChips(WidgetRef ref, ClassState classState) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: classState.categories.map((category) {
          final isSelected = category == classState.selectedCategory;
          final isLast = category == classState.categories.last;

          return Padding(
            padding: EdgeInsets.only(right: isLast ? 0 : Insets.s),
            child: _buildFilterChip(ref, category, isSelected),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildFilterChip(WidgetRef ref, String label, bool isSelected) {
    return InkWell(
      onTap: () => ref.read(classNotifierProvider.notifier).filterByCategory(label),
      borderRadius: BorderRadius.circular(AppSizes.buttonRadiusSm),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: Insets.l,
          vertical: Insets.s,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryColor : AppColors.surfaceColor,
          borderRadius: BorderRadius.circular(AppSizes.buttonRadiusSm),
          border: Border.all(
            color: isSelected ? AppColors.primaryColor : AppColors.inputBorderColor,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyles.body2.copyWith(
            color: isSelected ? AppColors.white : AppColors.primaryTextColor,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedClasses(ClassState classState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Featured Classes',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        Container(
          height: 200,
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow,
          ),
          child: classState.isLoading
              ? _buildLoadingState()
              : classState.featuredClasses.isEmpty
                  ? _buildEmptyFeaturedState()
                  : _buildFeaturedClassesList(classState.featuredClasses),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
      ),
    );
  }

  Widget _buildEmptyFeaturedState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.star_outline_rounded,
            size: AppSizes.iconXl,
            color: AppColors.secondaryTextColor,
          ),
          const SizedBox(height: Insets.s),
          Text(
            'Featured classes coming soon',
            style: TextStyles.body2.copyWith(
              color: AppColors.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedClassesList(List<dynamic> featuredClasses) {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.all(Insets.m),
      itemCount: featuredClasses.length,
      separatorBuilder: (context, index) => const SizedBox(width: Insets.m),
      itemBuilder: (context, index) {
        final gymClass = featuredClasses[index];
        return _buildFeaturedClassCard(gymClass);
      },
    );
  }

  Widget _buildFeaturedClassCard(dynamic gymClass) {
    return Container(
      width: 280,
      padding: const EdgeInsets.all(Insets.m),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(AppSizes.radiusMd),
        border: Border.all(
          color: AppColors.inputBorderColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  gymClass.name,
                  style: TextStyles.body1.copyWith(
                    color: AppColors.primaryTextColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: Insets.s,
                  vertical: Insets.xs,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSizes.buttonRadiusSm),
                ),
                child: Text(
                  'Featured',
                  style: TextStyles.caption.copyWith(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: Insets.s),
          Text(
            gymClass.description,
            style: TextStyles.body2.copyWith(
              color: AppColors.secondaryTextColor,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: Insets.s),
          Row(
            children: [
              Icon(
                Icons.person_outline_rounded,
                size: AppSizes.iconSm,
                color: AppColors.secondaryTextColor,
              ),
              const SizedBox(width: Insets.xs),
              Text(
                gymClass.instructor,
                style: TextStyles.body2.copyWith(
                  color: AppColors.secondaryTextColor,
                ),
              ),
              const Spacer(),
              Text(
                '${gymClass.duration} min',
                style: TextStyles.body2.copyWith(
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAllClasses(BuildContext context, ClassState classState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'All Classes',
              style: TextStyles.h2b.copyWith(
                color: AppColors.primaryTextColor,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Implement view all
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('View all feature coming soon!'),
                    backgroundColor: AppColors.infoColor,
                  ),
                );
              },
              child: Text(
                'View All',
                style: TextStyles.body2.copyWith(
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: Insets.m),
        if (classState.isLoading)
          _buildClassesLoadingState()
        else if (classState.classes.isEmpty)
          _buildEmptyClassesState()
        else
          _buildClassesList(classState.classes),
      ],
    );
  }

  Widget _buildClassesLoadingState() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 3,
      separatorBuilder: (context, index) => const SizedBox(height: Insets.m),
      itemBuilder: (context, index) => _buildLoadingClassCard(),
    );
  }

  Widget _buildLoadingClassCard() {
    return Container(
      height: 100,
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.inputBorderColor,
              borderRadius: BorderRadius.circular(AppSizes.cardRadiusSm),
            ),
          ),
          const SizedBox(width: Insets.m),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: double.infinity,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppColors.inputBorderColor,
                    borderRadius: BorderRadius.circular(AppSizes.radiusSm),
                  ),
                ),
                const SizedBox(height: Insets.s),
                Container(
                  width: 100,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppColors.inputBorderColor,
                    borderRadius: BorderRadius.circular(AppSizes.radiusSm),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyClassesState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Insets.xl),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.fitness_center_outlined,
              size: AppSizes.iconXl,
              color: AppColors.secondaryTextColor,
            ),
            const SizedBox(height: Insets.s),
            Text(
              'No classes available',
              style: TextStyles.body2.copyWith(
                color: AppColors.secondaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClassesList(List<dynamic> classes) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: classes.length,
      separatorBuilder: (context, index) => const SizedBox(height: Insets.m),
      itemBuilder: (context, index) => _buildClassCard(classes[index]),
    );
  }

  Widget _buildClassCard(dynamic gymClass) {
    return Builder(
      builder: (context) => InkWell(
        onTap: () {
          // TODO: Navigate to class details
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${gymClass.name} details coming soon!'),
              backgroundColor: AppColors.infoColor,
            ),
          );
        },
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        child: Container(
          padding: const EdgeInsets.all(Insets.l),
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow,
          ),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSizes.cardRadiusSm),
                ),
                child: Icon(
                  Icons.fitness_center_rounded,
                  color: AppColors.primaryColor,
                  size: AppSizes.iconLg,
                ),
              ),
              const SizedBox(width: Insets.m),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            gymClass.name,
                            style: TextStyles.body1.copyWith(
                              color: AppColors.primaryTextColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Text(
                          '\$${gymClass.price.toStringAsFixed(0)}',
                          style: TextStyles.body1.copyWith(
                            color: AppColors.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: Insets.xs),
                    Text(
                      gymClass.category,
                      style: TextStyles.caption.copyWith(
                        color: AppColors.secondaryTextColor,
                      ),
                    ),
                    const SizedBox(height: Insets.xs),
                    Row(
                      children: [
                        Text(
                          '${gymClass.duration} min',
                          style: TextStyles.caption.copyWith(
                            color: AppColors.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: Insets.m),
                        Text(
                          '${gymClass.availableSpots} spots left',
                          style: TextStyles.caption.copyWith(
                            color: gymClass.availableSpots <= 3 ? AppColors.warningColor : AppColors.successColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios_rounded,
                color: AppColors.secondaryTextColor,
                size: AppSizes.iconSm,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
