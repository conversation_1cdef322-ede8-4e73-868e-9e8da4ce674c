class ClassState {
  final bool isLoading;
  final String? error;
  final List<GymClass> classes;
  final List<String> categories;
  final String? selectedCategory;
  final List<GymClass> featuredClasses;

  const ClassState({
    this.isLoading = false,
    this.error,
    this.classes = const [],
    this.categories = const [],
    this.selectedCategory,
    this.featuredClasses = const [],
  });

  ClassState copyWith({
    bool? isLoading,
    String? error,
    List<GymClass>? classes,
    List<String>? categories,
    String? selectedCategory,
    List<GymClass>? featuredClasses,
  }) {
    return ClassState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      classes: classes ?? this.classes,
      categories: categories ?? this.categories,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      featuredClasses: featuredClasses ?? this.featuredClasses,
    );
  }
}

class GymClass {
  final String id;
  final String name;
  final String description;
  final String instructor;
  final String category;
  final int duration; // in minutes
  final int capacity;
  final int enrolled;
  final String difficulty; // Beginner, Intermediate, Advanced
  final String imageUrl;
  final List<DateTime> schedule;
  final double price;
  final bool isFeatured;

  const GymClass({
    required this.id,
    required this.name,
    required this.description,
    required this.instructor,
    required this.category,
    required this.duration,
    required this.capacity,
    required this.enrolled,
    required this.difficulty,
    required this.imageUrl,
    required this.schedule,
    required this.price,
    this.isFeatured = false,
  });

  bool get isAvailable => enrolled < capacity;
  int get availableSpots => capacity - enrolled;
  double get fillPercentage => enrolled / capacity;
}

class ClassBooking {
  final String id;
  final String classId;
  final String customerId;
  final DateTime scheduledTime;
  final String status; // booked, cancelled, completed
  final DateTime bookingDate;

  const ClassBooking({
    required this.id,
    required this.classId,
    required this.customerId,
    required this.scheduledTime,
    required this.status,
    required this.bookingDate,
  });
}
