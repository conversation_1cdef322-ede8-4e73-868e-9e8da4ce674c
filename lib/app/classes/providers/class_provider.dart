import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:techrar_gym/app/classes/models/class_state.dart';
import 'package:techrar_gym/app/classes/services/class_service.dart';

part 'class_provider.g.dart';

@riverpod
class ClassNotifier extends _$ClassNotifier {
  @override
  ClassState build() {
    // Initialize with loading state and trigger data loading asynchronously
    Future.microtask(() => loadClassData());
    return const ClassState(isLoading: true);
  }

  Future<void> loadClassData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(classServiceProvider);

      // Load all data concurrently
      final results = await Future.wait([
        service.getClasses(),
        service.getCategories(),
        service.getFeaturedClasses(),
      ]);

      final classesResult = results[0] as dynamic;
      final categoriesResult = results[1] as dynamic;
      final featuredClassesResult = results[2] as dynamic;

      // Check for errors
      String? error;
      List<GymClass> classes = [];
      List<String> categories = [];
      List<GymClass> featuredClasses = [];

      classesResult.fold(
        (err) => error ??= err,
        (data) => classes = data,
      );

      categoriesResult.fold(
        (err) => error ??= err,
        (data) => categories = data,
      );

      featuredClassesResult.fold(
        (err) => error ??= err,
        (data) => featuredClasses = data,
      );

      state = state.copyWith(
        isLoading: false,
        error: error,
        classes: classes,
        categories: categories,
        featuredClasses: featuredClasses,
        selectedCategory: categories.isNotEmpty ? categories.first : null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load class data: ${e.toString()}',
      );
    }
  }

  Future<void> filterByCategory(String category) async {
    state = state.copyWith(selectedCategory: category, isLoading: true);

    try {
      final service = ref.read(classServiceProvider);
      final result = await service.getClasses(
        category: category == 'All' ? null : category,
      );

      result.fold(
        (error) {
          state = state.copyWith(
            isLoading: false,
            error: error,
          );
        },
        (classes) {
          state = state.copyWith(
            isLoading: false,
            classes: classes,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to filter classes: ${e.toString()}',
      );
    }
  }

  Future<bool> bookClass({
    required String classId,
    required DateTime scheduledTime,
  }) async {
    try {
      final service = ref.read(classServiceProvider);
      final result = await service.bookClass(
        classId: classId,
        scheduledTime: scheduledTime,
      );

      return result.fold(
        (error) {
          state = state.copyWith(error: error);
          return false;
        },
        (booking) {
          // Refresh class data to update enrollment numbers
          loadClassData();
          return true;
        },
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to book class: ${e.toString()}');
      return false;
    }
  }

  Future<void> refresh() async {
    await loadClassData();
  }
}
