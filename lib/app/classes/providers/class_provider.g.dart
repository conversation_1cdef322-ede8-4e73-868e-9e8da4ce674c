// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'class_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$classNotifierHash() => r'88a9788fe429693bb0c472701c336f69cd45048e';

/// See also [ClassNotifier].
@ProviderFor(ClassNotifier)
final classNotifierProvider =
    AutoDisposeNotifierProvider<ClassNotifier, ClassState>.internal(
  ClassNotifier.new,
  name: r'classNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$classNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ClassNotifier = AutoDisposeNotifier<ClassState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
