import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:techrar_gym/app/training/models/training_state.dart';

final trainingServiceProvider = Provider<TrainingService>(
  (ref) => TrainingService(ref),
);

class TrainingService {
  final Ref _ref;
  TrainingService(this._ref);

  Future<Either<String, List<Workout>>> getWorkouts({String? category}) async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));
      
      final allWorkouts = [
        Workout(
          id: '1',
          name: 'Full Body Strength',
          description: 'Complete full body workout targeting all major muscle groups',
          category: 'Strength',
          duration: 45,
          difficulty: 'Intermediate',
          exercises: [
            const Exercise(
              id: '1',
              name: 'Push-ups',
              description: 'Standard push-ups for chest and arms',
              sets: 3,
              reps: 12,
              restTime: 60,
              imageUrl: 'https://example.com/pushups.jpg',
            ),
            const Exercise(
              id: '2',
              name: 'Squats',
              description: 'Bodyweight squats for legs and glutes',
              sets: 3,
              reps: 15,
              restTime: 60,
              imageUrl: 'https://example.com/squats.jpg',
            ),
          ],
          imageUrl: 'https://example.com/fullbody.jpg',
          caloriesBurned: 350,
          isFavorite: true,
        ),
        Workout(
          id: '2',
          name: 'Cardio Blast',
          description: 'High-intensity cardio workout for maximum calorie burn',
          category: 'Cardio',
          duration: 30,
          difficulty: 'Advanced',
          exercises: [
            const Exercise(
              id: '3',
              name: 'Jumping Jacks',
              description: 'Full body cardio exercise',
              sets: 3,
              reps: 0,
              duration: 45,
              restTime: 15,
              imageUrl: 'https://example.com/jumpingjacks.jpg',
            ),
            const Exercise(
              id: '4',
              name: 'Burpees',
              description: 'Full body explosive movement',
              sets: 3,
              reps: 10,
              restTime: 30,
              imageUrl: 'https://example.com/burpees.jpg',
            ),
          ],
          imageUrl: 'https://example.com/cardio.jpg',
          caloriesBurned: 400,
          isFavorite: false,
        ),
      ];

      if (category != null && category.isNotEmpty && category != 'All') {
        return right(allWorkouts.where((w) => w.category == category).toList());
      }

      return right(allWorkouts);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, List<WorkoutCategory>>> getCategories() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 300));
      
      return right([
        const WorkoutCategory(
          id: '1',
          name: 'All',
          description: 'All workout types',
          icon: 'fitness_center',
          workoutCount: 25,
        ),
        const WorkoutCategory(
          id: '2',
          name: 'Strength',
          description: 'Build muscle and strength',
          icon: 'fitness_center',
          workoutCount: 12,
        ),
        const WorkoutCategory(
          id: '3',
          name: 'Cardio',
          description: 'Improve cardiovascular health',
          icon: 'directions_run',
          workoutCount: 8,
        ),
        const WorkoutCategory(
          id: '4',
          name: 'Flexibility',
          description: 'Improve flexibility and mobility',
          icon: 'self_improvement',
          workoutCount: 5,
        ),
      ]);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, List<Workout>>> getRecentWorkouts() async {
    try {
      final workoutsResult = await getWorkouts();
      return workoutsResult.fold(
        (error) => left(error),
        (workouts) => right(workouts.take(3).toList()),
      );
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, WorkoutStats>> getWorkoutStats() async {
    try {
      // For now, return static data
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));
      
      return right(const WorkoutStats(
        totalWorkouts: 45,
        totalMinutes: 1350,
        totalCalories: 15750,
        currentStreak: 7,
        longestStreak: 15,
        categoryBreakdown: {
          'Strength': 25,
          'Cardio': 15,
          'Flexibility': 5,
        },
      ));
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, WorkoutSession>> startWorkout(String workoutId) async {
    try {
      // For now, simulate starting a workout
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));
      
      final session = WorkoutSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        workoutId: workoutId,
        startTime: DateTime.now(),
        status: 'in_progress',
      );

      return right(session);
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, WorkoutSession>> completeWorkout({
    required String sessionId,
    required int actualDuration,
    required int caloriesBurned,
  }) async {
    try {
      // For now, simulate completing a workout
      // TODO: Implement API call when backend is ready
      await Future.delayed(const Duration(milliseconds: 500));
      
      final session = WorkoutSession(
        id: sessionId,
        workoutId: 'workout_id',
        startTime: DateTime.now().subtract(Duration(minutes: actualDuration)),
        endTime: DateTime.now(),
        actualDuration: actualDuration,
        caloriesBurned: caloriesBurned,
        status: 'completed',
      );

      return right(session);
    } catch (e) {
      return left(e.toString());
    }
  }
}
