class TrainingState {
  final bool isLoading;
  final String? error;
  final List<Workout> workouts;
  final List<WorkoutCategory> categories;
  final String? selectedCategory;
  final List<Workout> recentWorkouts;
  final WorkoutStats? stats;

  const TrainingState({
    this.isLoading = false,
    this.error,
    this.workouts = const [],
    this.categories = const [],
    this.selectedCategory,
    this.recentWorkouts = const [],
    this.stats,
  });

  TrainingState copyWith({
    bool? isLoading,
    String? error,
    List<Workout>? workouts,
    List<WorkoutCategory>? categories,
    String? selectedCategory,
    List<Workout>? recentWorkouts,
    WorkoutStats? stats,
  }) {
    return TrainingState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      workouts: workouts ?? this.workouts,
      categories: categories ?? this.categories,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      recentWorkouts: recentWorkouts ?? this.recentWorkouts,
      stats: stats ?? this.stats,
    );
  }
}

class Workout {
  final String id;
  final String name;
  final String description;
  final String category;
  final int duration; // in minutes
  final String difficulty; // Beginner, Intermediate, Advanced
  final List<Exercise> exercises;
  final String imageUrl;
  final int caloriesBurned;
  final bool isFavorite;

  const Workout({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.duration,
    required this.difficulty,
    required this.exercises,
    required this.imageUrl,
    required this.caloriesBurned,
    this.isFavorite = false,
  });
}

class Exercise {
  final String id;
  final String name;
  final String description;
  final int sets;
  final int reps;
  final int? duration; // in seconds for time-based exercises
  final int? restTime; // in seconds
  final String? equipment;
  final String imageUrl;

  const Exercise({
    required this.id,
    required this.name,
    required this.description,
    required this.sets,
    required this.reps,
    this.duration,
    this.restTime,
    this.equipment,
    required this.imageUrl,
  });
}

class WorkoutCategory {
  final String id;
  final String name;
  final String description;
  final String icon;
  final int workoutCount;

  const WorkoutCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.workoutCount,
  });
}

class WorkoutStats {
  final int totalWorkouts;
  final int totalMinutes;
  final int totalCalories;
  final int currentStreak;
  final int longestStreak;
  final Map<String, int> categoryBreakdown;

  const WorkoutStats({
    required this.totalWorkouts,
    required this.totalMinutes,
    required this.totalCalories,
    required this.currentStreak,
    required this.longestStreak,
    required this.categoryBreakdown,
  });
}

class WorkoutSession {
  final String id;
  final String workoutId;
  final DateTime startTime;
  final DateTime? endTime;
  final int? actualDuration;
  final int? caloriesBurned;
  final String status; // in_progress, completed, paused
  final Map<String, dynamic>? exerciseProgress;

  const WorkoutSession({
    required this.id,
    required this.workoutId,
    required this.startTime,
    this.endTime,
    this.actualDuration,
    this.caloriesBurned,
    required this.status,
    this.exerciseProgress,
  });
}
