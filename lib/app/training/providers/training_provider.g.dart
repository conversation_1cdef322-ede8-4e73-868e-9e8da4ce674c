// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'training_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$trainingNotifierHash() => r'ec9ae31fdec4f9dccf024f25051c957ac2576c87';

/// See also [TrainingNotifier].
@ProviderFor(TrainingNotifier)
final trainingNotifierProvider =
    AutoDisposeNotifierProvider<TrainingNotifier, TrainingState>.internal(
  TrainingNotifier.new,
  name: r'trainingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$trainingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TrainingNotifier = AutoDisposeNotifier<TrainingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
