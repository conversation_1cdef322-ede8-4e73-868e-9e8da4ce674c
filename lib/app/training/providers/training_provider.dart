import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:techrar_gym/app/training/models/training_state.dart';
import 'package:techrar_gym/app/training/services/training_service.dart';

part 'training_provider.g.dart';

@riverpod
class TrainingNotifier extends _$TrainingNotifier {
  @override
  TrainingState build() {
    // Initialize with loading state and trigger data loading asynchronously
    Future.microtask(() => loadTrainingData());
    return const TrainingState(isLoading: true);
  }

  Future<void> loadTrainingData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(trainingServiceProvider);

      // Load all data concurrently
      final results = await Future.wait([
        service.getWorkouts(),
        service.getCategories(),
        service.getRecentWorkouts(),
        service.getWorkoutStats(),
      ]);

      final workoutsResult = results[0] as dynamic;
      final categoriesResult = results[1] as dynamic;
      final recentWorkoutsResult = results[2] as dynamic;
      final statsResult = results[3] as dynamic;

      // Check for errors
      String? error;
      List<Workout> workouts = [];
      List<WorkoutCategory> categories = [];
      List<Workout> recentWorkouts = [];
      WorkoutStats? stats;

      workoutsResult.fold(
        (err) => error ??= err,
        (data) => workouts = data,
      );

      categoriesResult.fold(
        (err) => error ??= err,
        (data) => categories = data,
      );

      recentWorkoutsResult.fold(
        (err) => error ??= err,
        (data) => recentWorkouts = data,
      );

      statsResult.fold(
        (err) => error ??= err,
        (data) => stats = data,
      );

      state = state.copyWith(
        isLoading: false,
        error: error,
        workouts: workouts,
        categories: categories,
        recentWorkouts: recentWorkouts,
        stats: stats,
        selectedCategory: categories.isNotEmpty ? categories.first.name : null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load training data: ${e.toString()}',
      );
    }
  }

  Future<void> filterByCategory(String category) async {
    state = state.copyWith(selectedCategory: category, isLoading: true);

    try {
      final service = ref.read(trainingServiceProvider);
      final result = await service.getWorkouts(category: category);

      result.fold(
        (error) {
          state = state.copyWith(
            isLoading: false,
            error: error,
          );
        },
        (workouts) {
          state = state.copyWith(
            isLoading: false,
            workouts: workouts,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to filter workouts: ${e.toString()}',
      );
    }
  }

  Future<bool> startWorkout(String workoutId) async {
    try {
      final service = ref.read(trainingServiceProvider);
      final result = await service.startWorkout(workoutId);

      return result.fold(
        (error) {
          state = state.copyWith(error: error);
          return false;
        },
        (session) {
          // TODO: Navigate to workout session screen
          return true;
        },
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to start workout: ${e.toString()}');
      return false;
    }
  }

  Future<void> refresh() async {
    await loadTrainingData();
  }
}
