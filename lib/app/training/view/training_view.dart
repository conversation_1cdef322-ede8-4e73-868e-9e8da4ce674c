import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/training/providers/training_provider.dart';
import 'package:techrar_gym/app/training/models/training_state.dart';

class TrainingView extends ConsumerWidget {
  const TrainingView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final trainingState = ref.watch(trainingNotifierProvider);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.backgroundColor,
        elevation: AppSizes.appBarElevation,
        title: Text(
          'Training',
          style: TextStyles.h2.copyWith(
            color: AppColors.primaryTextColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement training history
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Training history feature coming soon!'),
                  backgroundColor: AppColors.infoColor,
                ),
              );
            },
            icon: Icon(
              Icons.history_rounded,
              color: AppColors.primaryTextColor,
              size: AppSizes.iconMd,
            ),
          ),
          const SizedBox(width: Insets.m),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(trainingNotifierProvider.notifier).refresh(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(Insets.l),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current Workout
              _buildCurrentWorkoutSection(),
              const SizedBox(height: Insets.xl),

              // Training Programs
              _buildTrainingProgramsSection(context, trainingState),
              const SizedBox(height: Insets.xl),

              // Quick Workouts
              _buildQuickWorkoutsSection(context, ref, trainingState),
              const SizedBox(height: Insets.xl),

              // Progress Stats
              _buildProgressStatsSection(trainingState),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // TODO: Implement start workout
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Start workout feature coming soon!'),
              backgroundColor: AppColors.infoColor,
            ),
          );
        },
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        icon: const Icon(Icons.play_arrow_rounded),
        label: const Text('Start Workout'),
      ),
    );
  }

  Widget _buildCurrentWorkoutSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Current Workout',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(Insets.l),
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradient,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow2,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.fitness_center_rounded,
                    color: AppColors.white,
                    size: AppSizes.iconLg,
                  ),
                  const SizedBox(width: Insets.m),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'No active workout',
                          style: TextStyles.h2b.copyWith(
                            color: AppColors.white,
                          ),
                        ),
                        const SizedBox(height: Insets.xs),
                        Text(
                          'Start a workout to track your progress',
                          style: TextStyles.body2.copyWith(
                            color: AppColors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTrainingProgramsSection(BuildContext context, TrainingState trainingState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Training Programs',
              style: TextStyles.h2b.copyWith(
                color: AppColors.primaryTextColor,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Implement view all programs
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('View all programs feature coming soon!'),
                    backgroundColor: AppColors.infoColor,
                  ),
                );
              },
              child: Text(
                'View All',
                style: TextStyles.body2.copyWith(
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: Insets.m),
        SizedBox(
          height: 180,
          child: trainingState.isLoading
              ? _buildLoadingProgramsList()
              : trainingState.workouts.isEmpty
                  ? _buildEmptyProgramsState()
                  : _buildProgramsList(context, trainingState.workouts),
        ),
      ],
    );
  }

  Widget _buildLoadingProgramsList() {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: 3,
      separatorBuilder: (context, index) => const SizedBox(width: Insets.m),
      itemBuilder: (context, index) => Container(
        width: 200,
        decoration: BoxDecoration(
          color: AppColors.inputBorderColor,
          borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        ),
      ),
    );
  }

  Widget _buildEmptyProgramsState() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.sports_gymnastics_outlined,
              size: AppSizes.iconXl,
              color: AppColors.secondaryTextColor,
            ),
            const SizedBox(height: Insets.s),
            Text(
              'No training programs available',
              style: TextStyles.body2.copyWith(
                color: AppColors.secondaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgramsList(BuildContext context, List<dynamic> workouts) {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: workouts.length,
      separatorBuilder: (context, index) => const SizedBox(width: Insets.m),
      itemBuilder: (context, index) => _buildProgramCard(context, workouts[index]),
    );
  }

  Widget _buildProgramCard(BuildContext context, dynamic workout) {
    return InkWell(
      onTap: () {
        // TODO: Navigate to workout details
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${workout.name} details coming soon!'),
            backgroundColor: AppColors.infoColor,
          ),
        );
      },
      borderRadius: BorderRadius.circular(AppSizes.cardRadius),
      child: Container(
        width: 200,
        padding: const EdgeInsets.all(Insets.l),
        decoration: BoxDecoration(
          color: AppColors.surfaceColor,
          borderRadius: BorderRadius.circular(AppSizes.cardRadius),
          boxShadow: Styles.unifiedShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSizes.cardRadiusSm),
              ),
              child: Icon(
                Icons.sports_gymnastics_rounded,
                color: AppColors.primaryColor,
                size: AppSizes.iconXl,
              ),
            ),
            const SizedBox(height: Insets.m),
            Text(
              workout.name,
              style: TextStyles.body1.copyWith(
                color: AppColors.primaryTextColor,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: Insets.xs),
            Text(
              workout.description,
              style: TextStyles.caption.copyWith(
                color: AppColors.secondaryTextColor,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${workout.duration} min',
                  style: TextStyles.caption.copyWith(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Icon(
                  Icons.arrow_forward_rounded,
                  color: AppColors.primaryColor,
                  size: AppSizes.iconSm,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickWorkoutsSection(BuildContext context, WidgetRef ref, TrainingState trainingState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Workouts',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        if (trainingState.isLoading)
          _buildQuickWorkoutsLoading()
        else if (trainingState.recentWorkouts.isEmpty)
          _buildQuickWorkoutsEmpty()
        else
          _buildQuickWorkoutsGrid(context, ref, trainingState.recentWorkouts),
      ],
    );
  }

  Widget _buildQuickWorkoutsLoading() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: Insets.m,
      mainAxisSpacing: Insets.m,
      childAspectRatio: 1.2,
      children: List.generate(4, (index) {
        return Container(
          decoration: BoxDecoration(
            color: AppColors.inputBorderColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
          ),
        );
      }),
    );
  }

  Widget _buildQuickWorkoutsEmpty() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Insets.xl),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.timer_outlined,
              size: AppSizes.iconXl,
              color: AppColors.secondaryTextColor,
            ),
            const SizedBox(height: Insets.s),
            Text(
              'No quick workouts available',
              style: TextStyles.body2.copyWith(
                color: AppColors.secondaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickWorkoutsGrid(BuildContext context, WidgetRef ref, List<dynamic> recentWorkouts) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: Insets.m,
      mainAxisSpacing: Insets.m,
      childAspectRatio: 1.2,
      children: recentWorkouts.take(4).map((workout) {
        return _buildQuickWorkoutCard(context, ref, workout);
      }).toList(),
    );
  }

  Widget _buildQuickWorkoutCard(BuildContext context, WidgetRef ref, dynamic workout) {
    return InkWell(
      onTap: () async {
        final success = await ref.read(trainingNotifierProvider.notifier).startWorkout(workout.id);

        if (success && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Started ${workout.name}!'),
              backgroundColor: AppColors.successColor,
            ),
          );
        }
      },
      borderRadius: BorderRadius.circular(AppSizes.cardRadius),
      child: Container(
        padding: const EdgeInsets.all(Insets.l),
        decoration: BoxDecoration(
          color: AppColors.surfaceColor,
          borderRadius: BorderRadius.circular(AppSizes.cardRadius),
          boxShadow: Styles.unifiedShadow,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getWorkoutIcon(workout.category),
              size: AppSizes.iconLg,
              color: AppColors.primaryColor,
            ),
            const SizedBox(height: Insets.s),
            Text(
              workout.name,
              style: TextStyles.body2.copyWith(
                color: AppColors.primaryTextColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: Insets.xs),
            Text(
              '${workout.duration} min',
              style: TextStyles.caption.copyWith(
                color: AppColors.secondaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressStatsSection(TrainingState trainingState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Progress Stats',
          style: TextStyles.h2b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.m),
        Container(
          padding: const EdgeInsets.all(Insets.l),
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: BorderRadius.circular(AppSizes.cardRadius),
            boxShadow: Styles.unifiedShadow,
          ),
          child: trainingState.isLoading
              ? _buildStatsLoading()
              : trainingState.stats == null
                  ? _buildStatsEmpty()
                  : _buildStatsContent(trainingState.stats!),
        ),
      ],
    );
  }

  Widget _buildStatsLoading() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(3, (index) {
            return Container(
              width: 80,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.inputBorderColor,
                borderRadius: BorderRadius.circular(AppSizes.radiusSm),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildStatsEmpty() {
    return Center(
      child: Text(
        'Start working out to see your progress here',
        style: TextStyles.caption.copyWith(
          color: AppColors.hintTextColor,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildStatsContent(dynamic stats) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem('Workouts', stats.totalWorkouts.toString(), Icons.fitness_center_rounded),
        _buildStatItem('Minutes', stats.totalMinutes.toString(), Icons.timer_rounded),
        _buildStatItem('Calories', _formatNumber(stats.totalCalories), Icons.local_fire_department_rounded),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppColors.primaryColor,
          size: AppSizes.iconMd,
        ),
        const SizedBox(height: Insets.xs),
        Text(
          value,
          style: TextStyles.h1b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.xs),
        Text(
          label,
          style: TextStyles.caption.copyWith(
            color: AppColors.secondaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  IconData _getWorkoutIcon(String category) {
    switch (category.toLowerCase()) {
      case 'cardio':
        return Icons.favorite_rounded;
      case 'strength':
        return Icons.fitness_center_rounded;
      case 'flexibility':
        return Icons.self_improvement_rounded;
      default:
        return Icons.timer_rounded;
    }
  }

  String _formatNumber(int number) {
    if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}k';
    }
    return number.toString();
  }
}
