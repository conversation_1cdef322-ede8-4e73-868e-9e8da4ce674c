import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:navigation_utils/navigation_utils.dart';
import 'package:techrar_gym/app/auth/providers/auth_provider.dart';
import 'package:techrar_gym/app/auth/views/auth_gard.dart';
import 'package:techrar_gym/core/navigation/routes.dart';
import 'package:techrar_gym/core/services/env_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize environment variables
  await EnvService.init();

  NavigationManager.init(
    mainRouterDelegate: DefaultRouterDelegate(navigationDataRoutes: routes),
    routeInformationParser: DefaultRouteInformationParser(),
  );

  // * This can start logic before app start running!
  final providerContainer = ProviderContainer();
  // * Only ready Not auto-dispose provider.
  providerContainer.read(authNotifierProvider);
  runApp(
    UncontrolledProviderScope(
      container: providerContainer,
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      routerDelegate: NavigationManager.instance.routerDelegate,
      routeInformationParser: NavigationManager.instance.routeInformationParser,
    );
  }
}
