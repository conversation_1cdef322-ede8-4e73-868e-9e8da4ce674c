import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' show ProviderBase, ProviderScope;
import 'package:intl/intl.dart' show DateFormat;

// import '../../main.dart'; // Commented out - not needed
// import '../controllers/app_config.dart'; // Commented out - missing file

extension ListX<T> on List<T> {
  /// Adds an item to the list if it is not already present else removes it.
  void toggle(T item) => contains(item) ? remove(item) : add(item);

  /// Maps each element of the list to a new value of type P using the provided function [fn].
  /// If an error occurs during the mapping of an element, the [onError] callback is invoked
  /// with the error and stack trace, and the element is skipped.
  ///
  /// - [fn]: A function that maps an element of type T to a value of type P.
  /// - [onError]: An optional callback that handles errors during mapping and also returns the failed items.
  ///
  /// Returns a new list of type P containing the successfully mapped elements.
  List<P> tryMap<P>(P Function(T item) fn, {void Function(T failedItem, Object err, StackTrace stackTrace)? onError}) {
    final List<P> result = [];
    for (final T element in this) {
      try {
        result.add(fn(element));
      } catch (err, stk) {
        onError?.call(element, err, stk);
      }
    }
    return result;
  }
}

extension StringCase on String {
  String get capitalize {
    return "${this[0].toUpperCase()}${substring(1)}";
  }

  String get lowerCase {
    return toLowerCase();
  }

  String get snakeCase {
    RegExp exp = RegExp(r'(?<=[a-z])[A-Z]');
    return replaceAllMapped(exp, (Match m) => ('_${m.group(0)}')).toLowerCase().replaceAll("-", "_");
  }
}

extension LocalizedInsets on EdgeInsets {
  EdgeInsets relative() {
    // Simplified - always return LTR for now
    return EdgeInsets.fromLTRB(left, top, right, bottom);
  }

  operator +(EdgeInsets insets) {
    return EdgeInsets.only(
        top: top + insets.top, left: left + insets.left, bottom: bottom + insets.bottom, right: right + insets.right);
  }
}

extension LocalizedBorder on Border {
  Border relative() {
    // Simplified - always return original for now
    return this;
  }
}

extension LocalizedAlignment on Alignment {
  Alignment relative() {
    // Simplified - always return original for now
    return this;
  }
}

extension HexColor on Color {
  static Color? fromHex(String hexString) {
    hexString = hexString.replaceAll("#", "");
    if (hexString.length == 6) {
      hexString = "FF$hexString";
    }
    if (hexString.length == 8) {
      return Color(int.tryParse("0x$hexString") ?? 0xFF000000);
    } else {
      return null;
    }
  }

  String get hex => '#${value.toRadixString(16).substring(2)}';

  static Color onSurfaceTextColor(Color color) {
    const int threshold = 110;
    final int hRed = color.red;
    final int hGreen = color.green;
    final int hBlue = color.blue;
    final cBrightness = ((hRed * 299) + (hGreen * 587) + (hBlue * 114)) / 1000;
    if (cBrightness > threshold) {
      return Colors.black; // bright colors - black font
    } else {
      return Colors.white; // dark colors - white font
    }
  }

  Color darker(double percentage) {
    assert(percentage >= 0 && percentage <= 1, 'Percentage must be between 0 and 1');
    final double f = 1 - percentage;
    return Color.fromARGB(
      alpha, // keep the alpha value the same
      (red * f).round(), // reduce the red channel by the percentage
      (green * f).round(), // reduce the green channel by the percentage
      (blue * f).round(), // reduce the blue channel by the percentage
    );
  }

  Color lighter(double percentage) {
    assert(percentage >= 0 && percentage <= 1, 'Percentage must be between 0 and 1');
    final double p = percentage;
    return Color.fromARGB(
      alpha, // keep the alpha value the same
      red + ((255 - red) * p).round(), // increase the red channel
      green + ((255 - green) * p).round(), // increase the green channel
      blue + ((255 - blue) * p).round(), // increase the blue channel
    );
  }
}

extension TextStyleExtensions on TextStyle {
  /// Shortcut for color
  TextStyle withColor(Color v) => copyWith(color: v);

  /// Shortcut for fontSize
  TextStyle withSize(double v) => copyWith(fontSize: v);

  /// Shortcut for fontWeight
  TextStyle withWeight(FontWeight v) => copyWith(fontWeight: v);
}

extension ArNumbersConverter on String {
  get validateNumbers {
    Map<String, String> arNumbersConverter = {
      "٠": "0",
      "١": "1",
      "٢": "2",
      "٣": "3",
      "٤": "4",
      "٥": "5",
      "٦": "6",
      "٧": "7",
      "٨": "8",
      "٩": "9",
    };
    return characters.map((e) => arNumbersConverter[e] ?? e).join();
  }
}

extension PrettyJson on Map {
  String get prettyJson {
    return const JsonEncoder.withIndent('  ').convert(this);
  }
}

extension MapUtils on Map {
  Map removeNulls() {
    return this..removeWhere((key, value) => value == null);
  }
}

extension TimeOfDayExtension on TimeOfDay {
  static TimeOfDay fromString(String time) {
    List<int> fromTime = List<int>.from(time.split(":").map((e) => int.parse(e)).toList());
    return TimeOfDay(hour: fromTime[0], minute: fromTime[1]);
  }

  static String toJson(TimeOfDay time) {
    return '${time.hour}:${time.minute}:00';
  }

  /// e.g. 12:00 AM
  String get dayTime12 {
    final temp = DateTime.now();
    return (DateFormat('hh:mm a', 'en')..useNativeDigits = false)
        .format(DateTime(temp.year, temp.month, temp.day, hour, minute));
  }

  /// e.g. 03:49:28 AM
  String get dateTimeString {
    final temp = DateTime.now();
    return (DateFormat('dd-MM-y hh:mm:ss a', 'en')..useNativeDigits = false)
        .format(DateTime(temp.year, temp.month, temp.day, hour, minute));
  }

  /// e.g. 22:26
  String get hm24 {
    final temp = DateTime.now();
    return (DateFormat.Hm('en')..useNativeDigits = false)
        .format(DateTime(temp.year, temp.month, temp.day, hour, minute));
  }

  bool isBefore(TimeOfDay other) {
    if (hour < other.hour) return true;
    if (hour == other.hour && minute < other.minute) return true;
    return false;
  }

  bool isAfter(TimeOfDay other) {
    if (hour > other.hour) return true;
    if (hour == other.hour && minute > other.minute) return true;
    return false;
  }
}

extension DateTimeExtension on DateTime {
  /// e.g. 12:00 AM
  String get dayTime12 {
    return (DateFormat('hh:mm a', 'en')..useNativeDigits = false).format(this);
  }

  /// e.g. Aug 10, 1999 12:00 AM
  String get yMMMMdhm {
    return (DateFormat.yMMMd('en')..useNativeDigits = false).add_jm().format(this);
  }

  /// e.g. Aug 10, 1999 12:00 AM
  String get intercomDateFormat {
    return (DateFormat.yMMMd('en')..useNativeDigits = false).add_jm().format(this);
  }

  /// e.g. Monday August 10, 1999 14:20
  String get eyMMMMdHm {
    return (DateFormat('EEEE MMM dd, y HH:mm', 'en')..useNativeDigits = false).format(this);
  }

  /// e.g.  Monday August 10, 1999
  String get eyMMMMd {
    return (DateFormat('EEEE MMM dd, y', 'en')..useNativeDigits = false).format(this);
  }

  /// e.g.  August 10, 1999
  String get yMMMMd {
    return (DateFormat('MMMM dd, y', 'en')..useNativeDigits = false).format(this);
    // return (DateFormat.yMMMMd('en')..useNativeDigits = false).format(this);
  }

  /// 10 August 12:00 AM
  String get dMMMMhm {
    return (DateFormat('dd MMMM hh:mm a', 'en')..useNativeDigits = false).format(this);
  }

  /// e.g. 10-08-1999
  String get dateString {
    return (DateFormat('dd-MM-y', 'en')..useNativeDigits = false).format(this);
  }

  String get apiDateString {
    return (DateFormat('y-MM-dd', 'en')..useNativeDigits = false).format(this);
  }

  /// e.g. 10-08-1999 03:49:28 AM
  String get dateTimeString {
    return (DateFormat('dd-MM-y hh:mm:ss a', 'en')..useNativeDigits = false).format(this);
  }

  /// e.g. 10/08/1999
  String get ddMMy {
    return (DateFormat('dd/MM/y', 'en')..useNativeDigits = false).format(this);
  }

  /// e.g. 22:26
  String get hm24 {
    return (DateFormat.Hm('en')..useNativeDigits = false).format(this);
  }

  /// e.g. 10/8/1999
  String get yMd {
    return (DateFormat.yMd('en')..useNativeDigits = false).format(this);
  }

  /// August 1999
  String get mMMMy {
    return (DateFormat('MMMM y ', 'en')..useNativeDigits = false).format(this);
  }

  /// 10 August
  String get dMMMM {
    return (DateFormat('dd MMMM', 'en')..useNativeDigits = false).format(this);
  }

  /// 10 August 1999
  String get dMMMMy {
    return (DateFormat('dd MMMM y', 'en')..useNativeDigits = false).format(this);
  }

  /// 10 Aug
  String get dMMM {
    return (DateFormat('dd MMM', 'en')..useNativeDigits = false).format(this);
  }

  /// e.g. Sun
  String get e {
    return (DateFormat('E', 'en')..useNativeDigits = false).format(this);
  }

  /// e.g. Sunday
  String get eeee {
    return (DateFormat('EEEE', 'en')..useNativeDigits = false).format(this);
  }

  DateTime addSeconds(int seconds) => add(Duration(seconds: seconds));

  DateTime addMinutes(int minutes) => add(Duration(minutes: minutes));

  DateTime addHours(int hours) => add(Duration(hours: hours));

  DateTime addDays(int days) => add(Duration(days: days));

  DateTime addWeeks(int weeks) => add(Duration(days: weeks * 7));

  int get numberOfDaysInMonth => DateTime(year, (month + 1), 0).day;

  bool isSameDay(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }
}

extension EnumExtensions<T extends Enum> on Iterable<T> {
  T? byNameSafe(String? name, {bool caseSensitive = false}) {
    if (name == null) return null;
    for (T value in this) {
      if (caseSensitive) {
        if (value.name.toLowerCase() == name.toLowerCase()) return value;
      } else {
        if (value.name == name) return value;
      }
    }

    return null;
  }
}

extension CSVParser on String {
  List<int> parseCSVToInt() {
    try {
      return [...(split(',').map((e) => int.parse(e)))];
    } catch (e) {
      return [];
    }
  }
}

extension DoubleX on double {
  /// Useful for displaying double values in a user-friendly way
  /// e.g. 12.3456 -> 12.35, 12.0 -> 12
  String format([int places = 2]) => toStringAsFixed(this % 1 == 0 ? 0 : places);
}

extension ContextX on BuildContext {
  /// Returns the current text direction of the app
  TextDirection get textDirection => Directionality.of(this);

  /// Returns the current screen dimesions of the app
  Size get mediaQuery => MediaQuery.sizeOf(this);

  /// Read the provider of type T using riverpod
  T readScope<T>(ProviderBase<T> provider) => ProviderScope.containerOf(this).read(provider);
}
