import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvService {
  static bool _isInitialized = false;

  /// Initialize the environment service by loading the .env file
  static Future<void> init() async {
    if (!_isInitialized) {
      await dotenv.load(fileName: '.env');
      _isInitialized = true;
    }
  }

  /// Get the merchant ID from environment variables
  static String get merchantId {
    _ensureInitialized();
    return dotenv.env['MERCHANT_ID'] ?? 'default-merchant-id';
  }

  /// Get the API base URL from environment variables
  static String get apiBaseUrl {
    _ensureInitialized();
    return dotenv.env['API_BASE_URL'] ?? 'https://api.techrar-gym.com';
  }

  /// Get the development API base URL from environment variables
  static String get devApiBaseUrl {
    _ensureInitialized();
    return dotenv.env['DEV_API_BASE_URL'] ?? 'http://localhost:3000';
  }

  /// Get the app name from environment variables
  static String get appName {
    _ensureInitialized();
    return dotenv.env['APP_NAME'] ?? 'Techrar Gym';
  }

  /// Get the app version from environment variables
  static String get appVersion {
    _ensureInitialized();
    return dotenv.env['APP_VERSION'] ?? '1.0.0';
  }

  /// Check if running in production mode
  static bool get isProduction {
    _ensureInitialized();
    return dotenv.env['ENVIRONMENT']?.toLowerCase() == 'production';
  }

  /// Ensure the service is initialized before accessing environment variables
  static void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError(
        'EnvService not initialized. Call EnvService.init() before accessing environment variables.',
      );
    }
  }

  /// Get any environment variable by key
  static String? get(String key) {
    _ensureInitialized();
    return dotenv.env[key];
  }

  /// Get environment variable with a default value
  static String getOrDefault(String key, String defaultValue) {
    _ensureInitialized();
    return dotenv.env[key] ?? defaultValue;
  }

  // Theme Colors
  /// Convert hex string to Color
  static Color _hexToColor(String hex, String fallback) {
    _ensureInitialized();
    final hexColor = dotenv.env[hex] ?? fallback;
    return Color(int.parse('FF$hexColor', radix: 16));
  }

  // Primary Colors
  static Color get primaryColor => _hexToColor('PRIMARY_COLOR', '6C63FF');
  static Color get primaryDarkColor => _hexToColor('PRIMARY_DARK_COLOR', '5A52E8');
  static Color get primaryLightColor => _hexToColor('PRIMARY_LIGHT_COLOR', '8B85FF');

  // Secondary Colors
  static Color get secondaryColor => _hexToColor('SECONDARY_COLOR', '03DAC6');
  static Color get secondaryDarkColor => _hexToColor('SECONDARY_DARK_COLOR', '018786');

  // Background Colors
  static Color get backgroundColor => _hexToColor('BACKGROUND_COLOR', 'F8F9FA');
  static Color get surfaceColor => _hexToColor('SURFACE_COLOR', 'FFFFFF');
  static Color get cardColor => _hexToColor('CARD_COLOR', 'FFFFFF');

  // Text Colors
  static Color get primaryTextColor => _hexToColor('PRIMARY_TEXT_COLOR', '1A1A1A');
  static Color get secondaryTextColor => _hexToColor('SECONDARY_TEXT_COLOR', '6B7280');
  static Color get hintTextColor => _hexToColor('HINT_TEXT_COLOR', '9CA3AF');

  // Status Colors
  static Color get successColor => _hexToColor('SUCCESS_COLOR', '10B981');
  static Color get errorColor => _hexToColor('ERROR_COLOR', 'EF4444');
  static Color get warningColor => _hexToColor('WARNING_COLOR', 'F59E0B');
  static Color get infoColor => _hexToColor('INFO_COLOR', '3B82F6');

  // Button Colors
  static Color get buttonPrimaryColor => _hexToColor('BUTTON_PRIMARY_COLOR', '6C63FF');
  static Color get buttonSecondaryColor => _hexToColor('BUTTON_SECONDARY_COLOR', 'F3F4F6');
  static Color get buttonDisabledColor => _hexToColor('BUTTON_DISABLED_COLOR', 'E5E7EB');

  // Input Colors
  static Color get inputBorderColor => _hexToColor('INPUT_BORDER_COLOR', 'E5E7EB');
  static Color get inputFocusedBorderColor => _hexToColor('INPUT_FOCUSED_BORDER_COLOR', '6C63FF');
  static Color get inputErrorBorderColor => _hexToColor('INPUT_ERROR_BORDER_COLOR', 'EF4444');
  static Color get inputBackgroundColor => _hexToColor('INPUT_BACKGROUND_COLOR', 'FFFFFF');
}
