import 'dart:convert';
import 'package:flutter/services.dart';

class CustomFontLoader {
  // Singleton pattern
  static final CustomFontLoader _instance = CustomFontLoader._internal();
  factory CustomFontLoader() => _instance;
  CustomFontLoader._internal();

  // Font status tracking
  final Map<String, bool> _loadedFonts = {};

  // Font family names
  static const String unifiedFontFamily = 'CustomFontUnified';
  static const String arabicFontFamily = 'CustomFontAr';
  static const String englishFontFamily = 'CustomFontEn';

  // Base path for fonts
  static const String _baseFontPath = 'assets/brand/fonts';

  // Font weights mapping
  static const Map<String, FontWeight> _weightMap = {
    'thin': FontWeight.w100,
    'extralight': FontWeight.w200,
    'light': FontWeight.w300,
    'regular': FontWeight.w400,
    'medium': FontWeight.w500,
    'semibold': FontWeight.w600,
    'bold': FontWeight.w700,
    'extrabold': FontWeight.w800,
    'black': FontWeight.w900,
  };

  // Getter methods for font status
  bool get hasUnifiedFont => _loadedFonts[unifiedFontFamily] ?? false;
  bool get hasArabicFont => _loadedFonts[arabicFontFamily] ?? false;
  bool get hasEnglishFont => _loadedFonts[englishFontFamily] ?? false;

  /// Main method to load fonts
  Future<void> loadFonts() async {
    try {
      // Get the manifest to check available assets
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = json.decode(manifestContent);

      // Filter font assets
      final fontFiles = manifestMap.keys.where((String key) => key.startsWith(_baseFontPath)).toList();

      // Load Arabic fonts first (if available)
      await _loadLanguageFonts(
        fontFiles.where((f) => f.contains('_ar')).toList(),
        arabicFontFamily,
      );

      // Load English fonts (if available)
      await _loadLanguageFonts(
        fontFiles.where((f) => f.contains('_en')).toList(),
        englishFontFamily,
      );

      // Load unified font as fallback (if available)
      if (!hasArabicFont || !hasEnglishFont) {
        await _loadUnifiedFont(fontFiles.where((f) => f.contains('unified')).toList());
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Load a unified font file
  Future<void> _loadUnifiedFont(List<String> files) async {
    if (files.isEmpty) {
      _loadedFonts[unifiedFontFamily] = false;
      return;
    }

    final fontPath = files.first;
    final fontLoader = FontLoader(unifiedFontFamily);

    try {
      fontLoader.addFont(rootBundle.load(fontPath));
      await fontLoader.load();
      _loadedFonts[unifiedFontFamily] = true;
    } catch (e) {
      _loadedFonts[unifiedFontFamily] = false;
    }
  }

  /// Load fonts for a specific language
  Future<void> _loadLanguageFonts(List<String> files, String familyName) async {
    try {
      if (files.isEmpty) {
        _loadedFonts[familyName] = false;
        return;
      }

      final fontLoader = FontLoader(familyName);

      if (files.length == 1) {
        // Single font file case
        await _loadSingleFont(files.first, fontLoader);
      } else {
        // Multiple font files case
        await _loadMultipleFonts(files, fontLoader);
      }

      await fontLoader.load();
      _loadedFonts[familyName] = true;
    } catch (e) {
      _loadedFonts[familyName] = false;
    }
  }

  /// Load a single font file
  Future<void> _loadSingleFont(String path, FontLoader fontLoader) async {
    try {
      fontLoader.addFont(rootBundle.load(path));
    } catch (e) {
      rethrow;
    }
  }

  /// Load multiple font files with different weights
  Future<void> _loadMultipleFonts(List<String> paths, FontLoader fontLoader) async {
    for (final path in paths) {
      try {
        // Extract weight from filename
        final weight = _extractWeightFromPath(path);
        if (weight != null) {
          fontLoader.addFont(rootBundle.load(path));
        }
      } catch (e) {
        // Continue loading other fonts even if one fails
      }
    }
  }

  /// Extract font weight from file path
  FontWeight? _extractWeightFromPath(String path) {
    final fileName = path.toLowerCase();
    for (final entry in _weightMap.entries) {
      if (fileName.contains(entry.key)) {
        return entry.value;
      }
    }
    // Default to regular if no weight is found
    return FontWeight.w400;
  }

  /// Get the appropriate font family based on the current locale
  String getFontFamily(String? languageCode) {
    if (languageCode == 'ar' && hasArabicFont) {
      return arabicFontFamily;
    }
    if (hasEnglishFont) {
      return englishFontFamily;
    }
    if (hasUnifiedFont) {
      return unifiedFontFamily; // Use unified font as fallback
    }

    if (languageCode == 'ar') return 'Almarai';
    return 'VarelaRound';
  }
}
