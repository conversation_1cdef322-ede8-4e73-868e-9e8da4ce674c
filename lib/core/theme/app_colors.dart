import 'package:flutter/material.dart';
import '../services/env_service.dart';

class AppColors {
  // Basic Colors (always static)
  static const Color transparent = Colors.transparent;
  static const Color white = Colors.white;
  static const Color black = Colors.black;

  // Primary Colors (from environment)
  static Color get primaryColor => EnvService.primaryColor;
  static Color get primaryDarkColor => EnvService.primaryDarkColor;
  static Color get primaryLightColor => EnvService.primaryLightColor;

  // Secondary Colors (from environment)
  static Color get secondaryColor => EnvService.secondaryColor;
  static Color get secondaryDarkColor => EnvService.secondaryDarkColor;

  // Background Colors (from environment)
  static Color get backgroundColor => EnvService.backgroundColor;
  static Color get surfaceColor => EnvService.surfaceColor;
  static Color get cardColor => EnvService.cardColor;

  // Text Colors (from environment)
  static Color get primaryTextColor => EnvService.primaryTextColor;
  static Color get secondaryTextColor => EnvService.secondaryTextColor;
  static Color get hintTextColor => EnvService.hintTextColor;
  static Color get placeholderTextColor => hintTextColor.withValues(alpha: 0.6);

  // Status Colors (from environment)
  static Color get successColor => EnvService.successColor;
  static Color get errorColor => EnvService.errorColor;
  static Color get warningColor => EnvService.warningColor;
  static Color get infoColor => EnvService.infoColor;

  // Button Colors (from environment)
  static Color get buttonPrimaryColor => EnvService.buttonPrimaryColor;
  static Color get buttonSecondaryColor => EnvService.buttonSecondaryColor;
  static Color get buttonDisabledColor => EnvService.buttonDisabledColor;
  static Color get buttonTextColor => white;
  static Color get buttonSecondaryTextColor => primaryTextColor;

  // Input Colors (from environment)
  static Color get inputBorderColor => EnvService.inputBorderColor;
  static Color get inputFocusedBorderColor => EnvService.inputFocusedBorderColor;
  static Color get inputErrorBorderColor => EnvService.inputErrorBorderColor;
  static Color get inputBackgroundColor => EnvService.inputBackgroundColor;

  // Shadow Colors
  static Color get shadowColor => black.withValues(alpha: 0.1);
  static Color get lightShadowColor => black.withValues(alpha: 0.05);

  // Gradient Colors
  static LinearGradient get primaryGradient => LinearGradient(
        colors: [primaryColor, primaryLightColor],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );

  static LinearGradient get backgroundGradient => LinearGradient(
        colors: [backgroundColor, backgroundColor.withValues(alpha: 0.8)],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );

  // Legacy colors for backward compatibility (mapped to new system)
  static Color get textColor => primaryTextColor;
  static Color get grey => secondaryTextColor;
  static Color get lightGrey => inputBorderColor;
  static Color get lightGrey2 => backgroundColor;
  static Color get hintFontsColor => hintTextColor;
  static Color get background2Color => backgroundColor.withValues(alpha: 0.95);

  // Extended Grey Palette
  static Color get grey2 => secondaryTextColor.withValues(alpha: 0.8);

  // Status Color Variants
  static Color get green => successColor;
  static Color get blue => infoColor;
  static Color get red => errorColor;
  static Color get orange => warningColor;

  // Special Colors
  static const Color violet = Color(0xFF3a0ca3);
  static const Color applePayButtonColor = Color(0xFF161415);
  static const Color tabbyButtonColor = Color(0xFF3EEDBF);

  // Core Design System Colors (derived from theme)
  static Color get light0 => inputBorderColor;
  static Color get light1 => backgroundColor;
  static Color get light2 => surfaceColor.withValues(alpha: 0.95);
  static Color get light3 => surfaceColor.withValues(alpha: 0.98);
  static Color get light4 => surfaceColor;
  static Color get dark1 => primaryTextColor;
  static Color get dark2 => secondaryTextColor;
  static Color get dark3 => hintTextColor;
  static Color get dark4 => placeholderTextColor;
}
