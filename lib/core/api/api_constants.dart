import '../services/env_service.dart';

class ApiConstants {
  // Dynamic URLs from environment
  static String get baseUrl => EnvService.apiBaseUrl;
  static String get devBaseUrl => EnvService.devApiBaseUrl;

  // Auth endpoints
  static const String register = '/api/customer-auth/register';
  static const String login = '/api/customer-auth/login';
  static const String logout = '/api/customer-auth/logout';
  static const String resetPassword = '/api/customer-auth/reset-password';
  static const String profile = '/api/customer-auth/profile';

  // Public endpoints
  static const String classes = '/api/public/classes';
  static const String bookings = '/api/public/bookings';
  static const String subscriptionPlans = '/api/public/subscription-plans';
  static const String subscriptions = '/api/public/subscriptions';
  static const String freezeSubscription = '/api/public/subscriptions/freeze';
  static const String cancelSubscription = '/api/public/subscriptions/cancel';

  // Headers
  static const String contentType = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearer = 'Bearer';

  // Dynamic merchant ID from environment
  static String get merchantId => EnvService.merchantId;
}
