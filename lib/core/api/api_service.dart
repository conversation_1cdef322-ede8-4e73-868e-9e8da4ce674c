import 'dart:convert';
import 'dart:io';
import 'package:fpdart/fpdart.dart';
import 'package:http/http.dart' as http;
import 'api_response.dart';
import 'api_constants.dart';
import 'api_exceptions.dart';
import '../services/storage_service.dart';

typedef FutureEither<T> = Future<Either<String, T>>;

class ApiService {
  static const bool _isProduction = false; // Set to false for development
  static String get baseUrl => _isProduction ? ApiConstants.baseUrl : ApiConstants.devBaseUrl;

  static Future<Map<String, String>> _getHeaders({bool includeAuth = true}) async {
    final headers = {
      'Content-Type': ApiConstants.contentType,
      'Merchant-ID': ApiConstants.merchantId,
    };

    if (includeAuth) {
      final token = await StorageService.getAccessToken();
      if (token != null) {
        headers['Authorization'] = '${ApiConstants.bearer} $token';
      }
    }

    return headers;
  }

  static Future<ApiResponse<T>> _handleResponse<T>(
    http.Response response,
    T Function(dynamic)? fromJsonT,
  ) async {
    try {
      final jsonResponse = jsonDecode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse.fromJson(jsonResponse, fromJsonT);
      } else {
        // Handle different error status codes
        switch (response.statusCode) {
          case 400:
            if (jsonResponse['errors'] != null) {
              final errors = (jsonResponse['errors'] as List).map((e) => ValidationError.fromJson(e)).toList();
              throw ValidationException(
                jsonResponse['message'] ?? 'Validation failed',
                errors,
              );
            }
            throw ApiException(
              jsonResponse['message'] ?? 'Bad request',
              statusCode: response.statusCode,
              data: jsonResponse,
            );
          case 401:
            throw AuthenticationException(
              jsonResponse['message'] ?? 'Authentication failed',
            );
          case 403:
            throw ApiException(
              jsonResponse['message'] ?? 'Access forbidden',
              statusCode: response.statusCode,
            );
          case 404:
            throw ApiException(
              jsonResponse['message'] ?? 'Resource not found',
              statusCode: response.statusCode,
            );
          case 500:
            throw ApiException(
              'Server error. Please try again later.',
              statusCode: response.statusCode,
            );
          default:
            throw ApiException(
              jsonResponse['message'] ?? 'An error occurred',
              statusCode: response.statusCode,
              data: jsonResponse,
            );
        }
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Failed to parse response: $e');
    }
  }

  static Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, String>? queryParams,
    bool includeAuth = true,
    T Function(dynamic)? fromJsonT,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      final headers = await _getHeaders(includeAuth: includeAuth);

      final response = await http.get(finalUri, headers: headers).timeout(const Duration(seconds: 30));

      return _handleResponse(response, fromJsonT);
    } on SocketException {
      throw NetworkException('No internet connection');
    } on HttpException {
      throw NetworkException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  static Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
    bool includeAuth = true,
    T Function(dynamic)? fromJsonT,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      final headers = await _getHeaders(includeAuth: includeAuth);

      final response = await http
          .post(
            finalUri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response, fromJsonT);
    } on SocketException {
      throw NetworkException('No internet connection');
    } on HttpException {
      throw NetworkException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  static Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
    bool includeAuth = true,
    T Function(dynamic)? fromJsonT,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      final headers = await _getHeaders(includeAuth: includeAuth);

      final response = await http
          .put(
            finalUri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response, fromJsonT);
    } on SocketException {
      throw NetworkException('No internet connection');
    } on HttpException {
      throw NetworkException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  static Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, String>? queryParams,
    bool includeAuth = true,
    T Function(dynamic)? fromJsonT,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      final headers = await _getHeaders(includeAuth: includeAuth);

      final response = await http.delete(finalUri, headers: headers).timeout(const Duration(seconds: 30));

      return _handleResponse(response, fromJsonT);
    } on SocketException {
      throw NetworkException('No internet connection');
    } on HttpException {
      throw NetworkException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }
}
