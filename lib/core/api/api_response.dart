import 'api_exceptions.dart';

class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final List<ValidationError>? errors;

  ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.errors,
  });

  String get errorMessage => message;

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null ? fromJsonT(json['data']) : json['data'],
      errors: json['errors'] != null ? (json['errors'] as List).map((e) => ValidationError.fromJson(e)).toList() : null,
    );
  }
}
